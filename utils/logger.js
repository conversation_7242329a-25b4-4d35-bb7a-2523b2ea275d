const fs = require('fs');
const path = require('path');

class TwilioLogger {
    constructor() {
        this.logDir = path.join(__dirname, '..', 'logs');
        this.ensureLogDirectory();
    }

    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    formatTimestamp() {
        return new Date().toISOString();
    }

    writeLog(type, data) {
        const timestamp = this.formatTimestamp();
        const logEntry = {
            timestamp,
            type,
            ...data
        };

        // Log para arquivo específico do tipo
        const filename = `${type}-${new Date().toISOString().split('T')[0]}.log`;
        const filepath = path.join(this.logDir, filename);
        
        fs.appendFileSync(filepath, JSON.stringify(logEntry) + '\n');

        // Log geral
        const generalFilepath = path.join(this.logDir, `twilio-${new Date().toISOString().split('T')[0]}.log`);
        fs.appendFileSync(generalFilepath, JSON.stringify(logEntry) + '\n');

        // Console log para desenvolvimento
        console.log(`[${timestamp}] ${type.toUpperCase()}:`, data);
    }

    // Logs específicos para diferentes tipos de eventos
    webhookReceived(req) {
        this.writeLog('webhook_received', {
            method: req.method,
            url: req.url,
            headers: {
                'content-type': req.headers['content-type'],
                'user-agent': req.headers['user-agent'],
                'x-twilio-signature': req.headers['x-twilio-signature']
            },
            body: req.body,
            query: req.query
        });
    }

    messageReceived(messageData) {
        this.writeLog('message_received', {
            messageSid: messageData.MessageSid,
            from: messageData.From,
            to: messageData.To,
            body: messageData.Body,
            conversationSid: messageData.ConversationSid,
            participantSid: messageData.ParticipantSid,
            author: messageData.Author
        });
    }

    messageSent(messageData) {
        this.writeLog('message_sent', {
            conversationSid: messageData.conversationSid,
            messageSid: messageData.messageSid,
            body: messageData.body,
            author: messageData.author,
            to: messageData.to
        });
    }

    typingIndicator(typingData) {
        this.writeLog('typing_indicator', {
            conversationSid: typingData.conversationSid,
            participantSid: typingData.participantSid,
            participant: typingData.participant,
            action: typingData.action // 'started' or 'ended'
        });
    }

    conversationEvent(eventData) {
        this.writeLog('conversation_event', {
            eventType: eventData.EventType,
            conversationSid: eventData.ConversationSid,
            participantSid: eventData.ParticipantSid,
            author: eventData.Author,
            body: eventData.Body,
            messageSid: eventData.MessageSid
        });
    }

    apiCall(apiData) {
        this.writeLog('api_call', {
            method: apiData.method,
            endpoint: apiData.endpoint,
            requestData: apiData.requestData,
            responseStatus: apiData.responseStatus,
            responseData: apiData.responseData,
            error: apiData.error
        });
    }

    error(errorData) {
        this.writeLog('error', {
            message: errorData.message,
            stack: errorData.stack,
            context: errorData.context
        });
    }

    // Método para ler logs recentes
    getRecentLogs(type = 'twilio', hours = 24) {
        const filename = `${type}-${new Date().toISOString().split('T')[0]}.log`;
        const filepath = path.join(this.logDir, filename);
        
        if (!fs.existsSync(filepath)) {
            return [];
        }

        const content = fs.readFileSync(filepath, 'utf8');
        const lines = content.trim().split('\n').filter(line => line);
        
        const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
        
        return lines
            .map(line => {
                try {
                    return JSON.parse(line);
                } catch (e) {
                    return null;
                }
            })
            .filter(entry => entry && new Date(entry.timestamp) > cutoffTime)
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }
}

module.exports = new TwilioLogger();
