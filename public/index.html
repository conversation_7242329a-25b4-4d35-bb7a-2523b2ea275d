<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twilio WhatsApp Typing Indicator Test</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://sdk.twilio.com/js/conversations/v2.4/twilio-conversations.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Twilio WhatsApp Typing Indicator</h1>
            <p>Teste da funcionalidade de indicador de digitação com WhatsApp</p>
        </header>

        <div class="main-content">
            <!-- Seção de Configuração -->
            <section class="config-section">
                <h2>📋 Configuração</h2>
                
                <div class="config-group">
                    <label for="userIdentity">Sua Identidade:</label>
                    <input type="text" id="userIdentity" placeholder="web-user" value="web-user">
                    <button id="connectBtn" class="btn btn-primary">Conectar</button>
                </div>

                <div class="status" id="connectionStatus">
                    <span class="status-indicator offline"></span>
                    <span class="status-text">Desconectado</span>
                </div>
            </section>

            <!-- Seção de Conversa -->
            <section class="conversation-section">
                <h2>💬 Conectar ao WhatsApp</h2>

                <div class="conversation-controls">
                    <div class="input-group">
                        <label for="conversationName">Nome da Conversa (se criar nova):</label>
                        <input type="text" id="conversationName" placeholder="Conversa WhatsApp Typing Test" value="Conversa WhatsApp Typing Test">
                    </div>

                    <div class="input-group">
                        <label for="whatsappNumber">Número WhatsApp (formato: +5511999999999):</label>
                        <input type="text" id="whatsappNumber" placeholder="+5551993590210" value="+5551993590210">
                        <small class="input-help">💡 O sistema irá usar conversa existente ou criar uma nova automaticamente</small>
                    </div>

                    <div class="input-group">
                        <button id="startConversationBtn" class="btn btn-primary btn-large">🚀 Conectar ao WhatsApp</button>
                        <small class="button-help">Conecta à conversa existente ou cria uma nova se necessário</small>
                    </div>

                    <div class="conversation-info" id="conversationInfo" style="display: none;">
                        <h3>✅ Conversa Ativa:</h3>
                        <p><strong>SID:</strong> <span id="conversationSid"></span></p>
                        <p><strong>Nome:</strong> <span id="conversationFriendlyName"></span></p>
                        <p><strong>WhatsApp:</strong> <span id="conversationWhatsApp"></span></p>
                        <p class="success-message">🎉 Agora você pode testar o typing indicator!</p>
                    </div>
                </div>
            </section>

            <!-- Seção de Chat -->
            <section class="chat-section">
                <h2>💭 Chat com Typing Indicator</h2>
                
                <div class="chat-container">
                    <div class="messages" id="messagesContainer">
                        <div class="message system">
                            <p>Conecte-se e crie uma conversa para começar a testar o typing indicator!</p>
                        </div>
                    </div>
                    
                    <div class="typing-indicator" id="typingIndicator" style="display: none;">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="typing-text">Alguém está digitando...</span>
                    </div>
                    
                    <div class="message-input">
                        <input type="text" id="messageInput" placeholder="Digite sua mensagem..." disabled>
                        <button id="sendBtn" class="btn btn-primary" disabled>Enviar</button>
                    </div>
                </div>
            </section>

            <!-- Seção de Logs -->
            <section class="logs-section">
                <h2>📝 Logs de Eventos</h2>
                <div class="logs" id="logsContainer">
                    <div class="log-entry">
                        <span class="timestamp">[Aguardando conexão...]</span>
                        <span class="log-message">Sistema iniciado</span>
                    </div>
                </div>
                <button id="clearLogsBtn" class="btn btn-secondary">Limpar Logs</button>
            </section>
        </div>

        <!-- Instruções -->
        <section class="instructions">
            <h2>📖 Como Usar</h2>
            <ol>
                <li><strong>Conectar:</strong> Clique em "Conectar" para se conectar ao Twilio Conversations</li>
                <li><strong>Conectar WhatsApp:</strong> Clique em "🚀 Conectar ao WhatsApp" (número já pré-configurado)</li>
                <li><strong>Sistema Inteligente:</strong> Automaticamente usa conversa existente ou cria nova se necessário</li>
                <li><strong>Testar Typing:</strong> Digite no campo de mensagem para enviar o typing indicator</li>
                <li><strong>Verificar WhatsApp:</strong> Veja no WhatsApp se o typing indicator aparece</li>
                <li><strong>Conversa Bidirecional:</strong> Digite no WhatsApp e veja na aplicação web</li>
            </ol>
            
            <div class="warning">
                <h3>⚠️ Importante:</h3>
                <ul>
                    <li>O número WhatsApp deve estar conectado ao Sandbox da Twilio</li>
                    <li>Envie "join [keyword]" para o número sandbox antes de testar</li>
                    <li>O typing indicator só funciona entre participantes conectados</li>
                </ul>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>
