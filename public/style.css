/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }
}

/* Sections */
section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.4rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 10px;
}

/* Config Section */
.config-group {
    display: flex;
    gap: 10px;
    align-items: end;
    margin-bottom: 15px;
}

.config-group label {
    font-weight: 600;
    color: #4a5568;
    min-width: 120px;
}

/* Status */
.status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 8px;
    background: #f7fafc;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background: #48bb78;
}

.status-indicator.offline {
    background: #f56565;
}

.status-indicator.connecting {
    background: #ed8936;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Input Groups */
.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #4a5568;
}

.input-group input {
    width: 100%;
    padding: 10px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
    transform: translateY(-1px);
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 700;
    width: 100%;
    margin-top: 10px;
}

.success-message {
    color: #22543d;
    font-weight: 600;
    background: #c6f6d5;
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 10px;
}

/* Chat Section */
.chat-section {
    grid-column: 1 / -1;
}

.chat-container {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
}

.messages {
    height: 400px;
    overflow-y: auto;
    padding: 20px;
    background: #f7fafc;
}

.message {
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
}

.message.sent {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.received {
    background: white;
    border: 1px solid #e2e8f0;
}

.message.system {
    background: #fed7d7;
    color: #c53030;
    text-align: center;
    max-width: 100%;
}

.message-meta {
    font-size: 12px;
    opacity: 0.7;
    margin-top: 5px;
}

/* Typing Indicator */
.typing-indicator {
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #cbd5e0;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.typing-text {
    font-style: italic;
    color: #718096;
    font-size: 14px;
}

/* Message Input */
.message-input {
    display: flex;
    padding: 15px;
    background: white;
    border-top: 1px solid #e2e8f0;
}

.message-input input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    margin-right: 10px;
    font-size: 14px;
}

.message-input input:focus {
    outline: none;
    border-color: #667eea;
}

/* Conversation Info */
.conversation-info {
    background: #e6fffa;
    border: 1px solid #81e6d9;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.conversation-info h3 {
    color: #234e52;
    margin-bottom: 10px;
}

.conversation-info p {
    margin-bottom: 5px;
    color: #285e61;
}

/* Logs Section */
.logs-section {
    grid-column: 1 / -1;
}

.logs {
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    margin-bottom: 15px;
}

.log-entry {
    margin-bottom: 8px;
    display: flex;
    gap: 10px;
}

.timestamp {
    color: #81e6d9;
    font-weight: bold;
    min-width: 120px;
}

.log-message {
    color: #e2e8f0;
}

.log-entry.error .log-message {
    color: #feb2b2;
}

.log-entry.success .log-message {
    color: #9ae6b4;
}

.log-entry.warning .log-message {
    color: #fbb6ce;
}

/* Instructions */
.instructions {
    grid-column: 1 / -1;
    background: #f0fff4;
    border: 1px solid #9ae6b4;
}

.instructions h2 {
    color: #22543d;
    border-bottom-color: #9ae6b4;
}

.instructions ol {
    margin-left: 20px;
    margin-bottom: 20px;
}

.instructions li {
    margin-bottom: 8px;
    color: #2f855a;
}

.warning {
    background: #fffaf0;
    border: 1px solid #f6ad55;
    border-radius: 8px;
    padding: 15px;
}

.warning h3 {
    color: #c05621;
    margin-bottom: 10px;
}

.warning ul {
    margin-left: 20px;
}

.warning li {
    color: #dd6b20;
    margin-bottom: 5px;
}

/* Scrollbar Styling */
.messages::-webkit-scrollbar,
.logs::-webkit-scrollbar {
    width: 8px;
}

.messages::-webkit-scrollbar-track,
.logs::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb,
.logs::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb:hover,
.logs::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
