// Estado da aplicação
let conversationsClient = null;
let activeConversation = null;
let currentUserIdentity = null;
let typingTimeout = null;
let autoRefreshLogs = false;
let autoRefreshInterval = null;

// Elementos DOM
const elements = {
    userIdentity: document.getElementById('userIdentity'),
    connectBtn: document.getElementById('connectBtn'),
    connectionStatus: document.getElementById('connectionStatus'),
    conversationName: document.getElementById('conversationName'),
    whatsappNumber: document.getElementById('whatsappNumber'),
    startConversationBtn: document.getElementById('startConversationBtn'),
    conversationInfo: document.getElementById('conversationInfo'),
    conversationSid: document.getElementById('conversationSid'),
    conversationFriendlyName: document.getElementById('conversationFriendlyName'),
    conversationWhatsApp: document.getElementById('conversationWhatsApp'),
    messagesContainer: document.getElementById('messagesContainer'),
    typingIndicator: document.getElementById('typingIndicator'),
    messageInput: document.getElementById('messageInput'),
    sendBtn: document.getElementById('sendBtn'),
    logsContainer: document.getElementById('logsContainer'),
    clearLogsBtn: document.getElementById('clearLogsBtn'),
    refreshLogsBtn: document.getElementById('refreshLogsBtn'),
    toggleAutoRefreshBtn: document.getElementById('toggleAutoRefreshBtn')
};

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    addLog('Sistema iniciado', 'success');
});

// Event Listeners
function setupEventListeners() {
    elements.connectBtn.addEventListener('click', handleConnect);
    elements.startConversationBtn.addEventListener('click', handleStartConversation);
    elements.sendBtn.addEventListener('click', handleSendMessage);
    elements.messageInput.addEventListener('keypress', handleMessageKeyPress);
    elements.messageInput.addEventListener('input', handleTyping);
    elements.clearLogsBtn.addEventListener('click', clearLogs);
    elements.refreshLogsBtn.addEventListener('click', refreshServerLogs);
    elements.toggleAutoRefreshBtn.addEventListener('click', toggleAutoRefresh);
}

// Conectar ao Twilio Conversations
async function handleConnect() {
    const identity = elements.userIdentity.value.trim();
    
    if (!identity) {
        addLog('Por favor, insira uma identidade', 'error');
        return;
    }

    try {
        updateConnectionStatus('connecting', 'Conectando...');
        elements.connectBtn.disabled = true;
        
        addLog(`Conectando como: ${identity}`);
        
        // Obter token de acesso
        const response = await fetch('/api/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ identity })
        });

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog('Token obtido com sucesso', 'success');

        // Inicializar cliente Conversations
        conversationsClient = new Twilio.Conversations.Client(data.token);
        currentUserIdentity = identity;

        // Event listeners do cliente
        conversationsClient.on('connectionStateChanged', handleConnectionStateChanged);
        conversationsClient.on('conversationJoined', handleConversationJoined);
        conversationsClient.on('conversationLeft', handleConversationLeft);

        addLog('Cliente Conversations inicializado', 'success');
        updateConnectionStatus('online', 'Conectado');

        elements.userIdentity.disabled = true;
        enableConversationControls();

    } catch (error) {
        console.error('Erro ao conectar:', error);
        addLog(`Erro ao conectar: ${error.message}`, 'error');
        updateConnectionStatus('offline', 'Erro na conexão');
        elements.connectBtn.disabled = false;
    }
}

// Iniciar conversa com WhatsApp (função inteligente)
async function handleStartConversation() {
    if (!conversationsClient) {
        addLog('Conecte-se primeiro', 'error');
        return;
    }

    const friendlyName = elements.conversationName.value.trim() || 'Conversa WhatsApp Typing Test';
    const whatsappNumber = elements.whatsappNumber.value.trim();

    if (!whatsappNumber) {
        addLog('Por favor, insira um número do WhatsApp', 'error');
        return;
    }

    try {
        elements.startConversationBtn.disabled = true;
        elements.startConversationBtn.textContent = '⏳ Conectando...';

        addLog(`🔍 Verificando conversa existente para ${whatsappNumber}...`);

        // Usar a nova API inteligente que verifica existência e conecta/cria automaticamente
        const response = await fetch('/api/conversations/connect-whatsapp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                whatsappNumber,
                friendlyName,
                userIdentity: currentUserIdentity
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog(`✅ ${data.message}`, 'success');

        // Log detalhado do que foi feito
        if (data.isNewConversation) {
            addLog('📝 Nova conversa criada', 'success');
        } else {
            addLog('🔄 Usando conversa existente', 'success');
        }

        if (data.participants.chat.exists) {
            addLog(`✅ Participante chat (${data.participants.chat.identity}) já estava na conversa`, 'success');
        } else {
            addLog(`✅ Participante chat (${data.participants.chat.identity}) adicionado`, 'success');
        }

        if (data.participants.whatsapp.exists) {
            addLog(`✅ Participante WhatsApp (${data.participants.whatsapp.address}) já estava na conversa`, 'success');
        } else {
            addLog(`✅ Participante WhatsApp (${data.participants.whatsapp.address}) adicionado`, 'success');
        }

        // Conectar-se à conversa via SDK
        addLog('🔗 Conectando ao SDK da conversa...');
        activeConversation = await conversationsClient.getConversationBySid(data.conversation.conversationSid);

        // Verificar se já está na conversa, se não, juntar-se
        try {
            await activeConversation.join();
            addLog('✅ Conectado à conversa via SDK', 'success');
        } catch (error) {
            // Se já estiver na conversa, isso é normal
            if (error.message.includes('already a participant') || error.message.includes('already joined')) {
                addLog('✅ Já conectado à conversa via SDK', 'success');
            } else {
                addLog(`⚠️ Aviso ao conectar SDK: ${error.message}`, 'warning');
            }
        }

        // Configurar eventos da conversa
        setupConversationEvents();

        // Atualizar interface
        updateConversationInfo({
            conversationSid: data.conversation.conversationSid,
            friendlyName: data.conversation.friendlyName,
            whatsappNumber: whatsappNumber
        });

        enableChatControls();

        // Mensagens de sucesso baseadas no que aconteceu
        if (data.isNewConversation) {
            addSystemMessage(`🎉 Nova conversa criada e configurada com sucesso!`);
        } else {
            addSystemMessage(`🔄 Conectado à conversa existente com sucesso!`);
        }

        addSystemMessage(`📱 WhatsApp: ${whatsappNumber}`);
        addSystemMessage(`💡 Agora você pode testar o typing indicator digitando no campo abaixo.`);

        addLog('🎉 Conversa WhatsApp pronta para uso!', 'success');

    } catch (error) {
        console.error('Erro ao conectar conversa:', error);
        addLog(`❌ Erro ao conectar conversa: ${error.message}`, 'error');

        // Resetar estado em caso de erro
        activeConversation = null;

    } finally {
        elements.startConversationBtn.disabled = false;
        elements.startConversationBtn.textContent = '🚀 Iniciar Conversa com WhatsApp';
    }
}



// Adicionar participante chat via API
async function addChatParticipant(conversationSid, identity) {
    try {
        const response = await fetch(`/api/conversations/${conversationSid}/participants/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ identity })
        });

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog(`Participante chat adicionado: ${data.identity}`, 'success');
        return data;

    } catch (error) {
        console.error('Erro ao adicionar participante chat:', error);
        addLog(`Erro ao adicionar participante chat: ${error.message}`, 'error');
        throw error;
    }
}

// Enviar mensagem
async function handleSendMessage() {
    if (!activeConversation) {
        addLog('Nenhuma conversa ativa', 'error');
        return;
    }

    const messageText = elements.messageInput.value.trim();
    
    if (!messageText) {
        return;
    }

    try {
        await activeConversation.sendMessage(messageText);
        elements.messageInput.value = '';
        addLog('Mensagem enviada', 'success');

    } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        addLog(`Erro ao enviar mensagem: ${error.message}`, 'error');
    }
}

// Manipular tecla Enter no input de mensagem
function handleMessageKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSendMessage();
    }
}

// Manipular typing indicator
function handleTyping() {
    if (!activeConversation) {
        return;
    }

    // Enviar typing indicator
    try {
        activeConversation.typing();
        addLog('Typing indicator enviado', 'success');
    } catch (error) {
        console.error('Erro ao enviar typing indicator:', error);
        addLog(`Erro ao enviar typing indicator: ${error.message}`, 'error');
    }
}

// Configurar eventos da conversa
function setupConversationEvents() {
    if (!activeConversation) return;

    // Mensagens
    activeConversation.on('messageAdded', handleMessageAdded);
    
    // Typing indicators
    activeConversation.on('typingStarted', handleTypingStarted);
    activeConversation.on('typingEnded', handleTypingEnded);
    
    // Participantes
    activeConversation.on('participantJoined', handleParticipantJoined);
    activeConversation.on('participantLeft', handleParticipantLeft);

    addLog('Event listeners da conversa configurados', 'success');
}

// Manipular nova mensagem
function handleMessageAdded(message) {
    addLog(`Nova mensagem de ${message.author || 'Sistema'}: ${message.body}`, 'success');
    
    const isOwnMessage = message.author === currentUserIdentity;
    addMessage(message.body, isOwnMessage ? 'sent' : 'received', message.author, message.dateCreated);
}

// Manipular início de digitação
function handleTypingStarted(participant) {
    if (participant.identity !== currentUserIdentity) {
        addLog(`${participant.identity || 'Alguém'} começou a digitar`, 'warning');
        showTypingIndicator(participant.identity || 'Alguém');
    }
}

// Manipular fim de digitação
function handleTypingEnded(participant) {
    if (participant.identity !== currentUserIdentity) {
        addLog(`${participant.identity || 'Alguém'} parou de digitar`, 'warning');
        hideTypingIndicator();
    }
}

// Manipular participante entrando
function handleParticipantJoined(participant) {
    addLog(`Participante entrou: ${participant.identity || participant.address}`, 'success');
    addSystemMessage(`${participant.identity || participant.address} entrou na conversa`);
}

// Manipular participante saindo
function handleParticipantLeft(participant) {
    addLog(`Participante saiu: ${participant.identity || participant.address}`, 'warning');
    addSystemMessage(`${participant.identity || participant.address} saiu da conversa`);
}

// Manipular mudança de estado de conexão
function handleConnectionStateChanged(state) {
    addLog(`Estado de conexão: ${state}`, 'warning');
    
    switch (state) {
        case 'connected':
            updateConnectionStatus('online', 'Conectado');
            break;
        case 'connecting':
            updateConnectionStatus('connecting', 'Conectando...');
            break;
        case 'disconnected':
            updateConnectionStatus('offline', 'Desconectado');
            break;
        case 'denied':
            updateConnectionStatus('offline', 'Conexão negada');
            break;
    }
}

// Manipular conversa juntada
function handleConversationJoined(conversation) {
    addLog(`Juntou-se à conversa: ${conversation.friendlyName}`, 'success');
}

// Manipular conversa deixada
function handleConversationLeft(conversation) {
    addLog(`Deixou a conversa: ${conversation.friendlyName}`, 'warning');
}

// Utilitários de UI
function updateConnectionStatus(status, text) {
    const indicator = elements.connectionStatus.querySelector('.status-indicator');
    const statusText = elements.connectionStatus.querySelector('.status-text');
    
    indicator.className = `status-indicator ${status}`;
    statusText.textContent = text;
}

function updateConversationInfo(conversationData) {
    elements.conversationSid.textContent = conversationData.conversationSid;
    elements.conversationFriendlyName.textContent = conversationData.friendlyName;
    if (conversationData.whatsappNumber) {
        elements.conversationWhatsApp.textContent = conversationData.whatsappNumber;
    }
    elements.conversationInfo.style.display = 'block';
}

function enableConversationControls() {
    elements.startConversationBtn.disabled = false;
}

function enableChatControls() {
    elements.messageInput.disabled = false;
    elements.sendBtn.disabled = false;
    elements.messageInput.focus();
}

function addMessage(text, type, author, timestamp) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    const messageContent = document.createElement('p');
    messageContent.textContent = text;
    messageDiv.appendChild(messageContent);
    
    if (author && timestamp) {
        const metaDiv = document.createElement('div');
        metaDiv.className = 'message-meta';
        metaDiv.textContent = `${author} - ${new Date(timestamp).toLocaleTimeString()}`;
        messageDiv.appendChild(metaDiv);
    }
    
    elements.messagesContainer.appendChild(messageDiv);
    elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
}

function addSystemMessage(text) {
    addMessage(text, 'system');
}

function showTypingIndicator(user) {
    const typingText = elements.typingIndicator.querySelector('.typing-text');
    typingText.textContent = `${user} está digitando...`;
    elements.typingIndicator.style.display = 'flex';
}

function hideTypingIndicator() {
    elements.typingIndicator.style.display = 'none';
}

function addLog(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    
    const timestamp = document.createElement('span');
    timestamp.className = 'timestamp';
    timestamp.textContent = `[${new Date().toLocaleTimeString()}]`;
    
    const logMessage = document.createElement('span');
    logMessage.className = 'log-message';
    logMessage.textContent = message;
    
    logEntry.appendChild(timestamp);
    logEntry.appendChild(logMessage);
    
    elements.logsContainer.appendChild(logEntry);
    elements.logsContainer.scrollTop = elements.logsContainer.scrollHeight;
}

function clearLogs() {
    elements.logsContainer.innerHTML = '';
    addLog('Logs limpos', 'success');
}

// Buscar logs do servidor
async function refreshServerLogs() {
    try {
        elements.refreshLogsBtn.disabled = true;
        elements.refreshLogsBtn.textContent = 'Carregando...';

        const response = await fetch('/webhooks/logs?hours=1');
        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();

        if (data.logs && data.logs.length > 0) {
            addLog(`📊 Carregados ${data.logs.length} logs do servidor`, 'success');

            // Adicionar logs do servidor
            data.logs.forEach(log => {
                const logType = getLogTypeFromServerLog(log);
                const message = formatServerLogMessage(log);
                addServerLog(message, logType, log.timestamp);
            });
        } else {
            addLog('📊 Nenhum log encontrado no servidor', 'warning');
        }

    } catch (error) {
        console.error('Erro ao buscar logs do servidor:', error);
        addLog(`❌ Erro ao buscar logs: ${error.message}`, 'error');
    } finally {
        elements.refreshLogsBtn.disabled = false;
        elements.refreshLogsBtn.textContent = 'Atualizar Logs';
    }
}

// Alternar auto-refresh de logs
function toggleAutoRefresh() {
    autoRefreshLogs = !autoRefreshLogs;

    if (autoRefreshLogs) {
        elements.toggleAutoRefreshBtn.textContent = 'Auto-refresh: ON';
        elements.toggleAutoRefreshBtn.classList.add('btn-primary');
        elements.toggleAutoRefreshBtn.classList.remove('btn-secondary');

        // Atualizar logs a cada 10 segundos
        autoRefreshInterval = setInterval(refreshServerLogs, 10000);
        addLog('🔄 Auto-refresh de logs ativado (10s)', 'success');
    } else {
        elements.toggleAutoRefreshBtn.textContent = 'Auto-refresh: OFF';
        elements.toggleAutoRefreshBtn.classList.remove('btn-primary');
        elements.toggleAutoRefreshBtn.classList.add('btn-secondary');

        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
        addLog('🔄 Auto-refresh de logs desativado', 'warning');
    }
}

// Determinar tipo de log baseado no log do servidor
function getLogTypeFromServerLog(log) {
    if (log.type.includes('error')) return 'error';
    if (log.type.includes('webhook')) return 'success';
    if (log.type.includes('message')) return 'success';
    if (log.type.includes('typing')) return 'warning';
    return 'info';
}

// Formatar mensagem do log do servidor
function formatServerLogMessage(log) {
    const timestamp = new Date(log.timestamp).toLocaleTimeString();

    switch (log.type) {
        case 'webhook_received':
            return `🌐 Webhook recebido: ${log.url} - ${log.body?.Body || log.body?.EventType || 'N/A'}`;
        case 'message_received':
            return `📱 Mensagem recebida: "${log.body}" de ${log.from}`;
        case 'message_sent':
            return `📤 Mensagem enviada: "${log.body}" para ${log.to}`;
        case 'typing_indicator':
            return `⌨️ Typing ${log.action}: ${log.participant}`;
        case 'conversation_event':
            return `💬 Evento: ${log.eventType} - ${log.body || 'N/A'}`;
        case 'error':
            return `❌ Erro: ${log.message}`;
        default:
            return `📋 ${log.type}: ${JSON.stringify(log).substring(0, 100)}...`;
    }
}

// Adicionar log do servidor com timestamp específico
function addServerLog(message, type = 'info', timestamp) {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry server-log ${type}`;

    const timestampSpan = document.createElement('span');
    timestampSpan.className = 'timestamp';
    timestampSpan.textContent = `[${new Date(timestamp).toLocaleTimeString()}]`;

    const logMessage = document.createElement('span');
    logMessage.className = 'log-message';
    logMessage.textContent = message;

    logEntry.appendChild(timestampSpan);
    logEntry.appendChild(logMessage);

    elements.logsContainer.appendChild(logEntry);
    elements.logsContainer.scrollTop = elements.logsContainer.scrollHeight;
}
