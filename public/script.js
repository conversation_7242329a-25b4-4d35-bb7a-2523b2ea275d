// Estado da aplicação
let conversationsClient = null;
let activeConversation = null;
let currentUserIdentity = null;
let typingTimeout = null;

// Elementos DOM
const elements = {
    userIdentity: document.getElementById('userIdentity'),
    connectBtn: document.getElementById('connectBtn'),
    connectionStatus: document.getElementById('connectionStatus'),
    conversationName: document.getElementById('conversationName'),
    whatsappNumber: document.getElementById('whatsappNumber'),
    startConversationBtn: document.getElementById('startConversationBtn'),
    conversationInfo: document.getElementById('conversationInfo'),
    conversationSid: document.getElementById('conversationSid'),
    conversationFriendlyName: document.getElementById('conversationFriendlyName'),
    conversationWhatsApp: document.getElementById('conversationWhatsApp'),
    messagesContainer: document.getElementById('messagesContainer'),
    typingIndicator: document.getElementById('typingIndicator'),
    messageInput: document.getElementById('messageInput'),
    sendBtn: document.getElementById('sendBtn'),
    logsContainer: document.getElementById('logsContainer'),
    clearLogsBtn: document.getElementById('clearLogsBtn')
};

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    addLog('Sistema iniciado', 'success');
});

// Event Listeners
function setupEventListeners() {
    elements.connectBtn.addEventListener('click', handleConnect);
    elements.startConversationBtn.addEventListener('click', handleStartConversation);
    elements.sendBtn.addEventListener('click', handleSendMessage);
    elements.messageInput.addEventListener('keypress', handleMessageKeyPress);
    elements.messageInput.addEventListener('input', handleTyping);
    elements.clearLogsBtn.addEventListener('click', clearLogs);
}

// Conectar ao Twilio Conversations
async function handleConnect() {
    const identity = elements.userIdentity.value.trim();
    
    if (!identity) {
        addLog('Por favor, insira uma identidade', 'error');
        return;
    }

    try {
        updateConnectionStatus('connecting', 'Conectando...');
        elements.connectBtn.disabled = true;
        
        addLog(`Conectando como: ${identity}`);
        
        // Obter token de acesso
        const response = await fetch('/api/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ identity })
        });

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog('Token obtido com sucesso', 'success');

        // Inicializar cliente Conversations
        conversationsClient = new Twilio.Conversations.Client(data.token);
        currentUserIdentity = identity;

        // Event listeners do cliente
        conversationsClient.on('connectionStateChanged', handleConnectionStateChanged);
        conversationsClient.on('conversationJoined', handleConversationJoined);
        conversationsClient.on('conversationLeft', handleConversationLeft);

        addLog('Cliente Conversations inicializado', 'success');
        updateConnectionStatus('online', 'Conectado');

        elements.userIdentity.disabled = true;
        enableConversationControls();

    } catch (error) {
        console.error('Erro ao conectar:', error);
        addLog(`Erro ao conectar: ${error.message}`, 'error');
        updateConnectionStatus('offline', 'Erro na conexão');
        elements.connectBtn.disabled = false;
    }
}

// Iniciar conversa com WhatsApp (função unificada)
async function handleStartConversation() {
    if (!conversationsClient) {
        addLog('Conecte-se primeiro', 'error');
        return;
    }

    const friendlyName = elements.conversationName.value.trim() || 'Conversa WhatsApp Typing Test';
    const whatsappNumber = elements.whatsappNumber.value.trim();

    if (!whatsappNumber) {
        addLog('Por favor, insira um número do WhatsApp', 'error');
        return;
    }

    try {
        elements.startConversationBtn.disabled = true;
        elements.startConversationBtn.textContent = '⏳ Criando conversa...';

        addLog(`Iniciando conversa: ${friendlyName} com ${whatsappNumber}`);

        // Passo 1: Criar conversa
        addLog('1/4 - Criando conversa...');
        const conversationResponse = await fetch('/api/conversations/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ friendlyName })
        });

        if (!conversationResponse.ok) {
            throw new Error(`Erro ao criar conversa: ${conversationResponse.status}`);
        }

        const conversationData = await conversationResponse.json();
        addLog(`✅ Conversa criada: ${conversationData.conversationSid}`, 'success');

        // Passo 2: Conectar-se à conversa via SDK
        addLog('2/4 - Conectando à conversa...');
        activeConversation = await conversationsClient.getConversationBySid(conversationData.conversationSid);

        // Verificar se já está na conversa, se não, juntar-se
        try {
            await activeConversation.join();
            addLog('✅ Juntou-se à conversa', 'success');
        } catch (error) {
            // Se já estiver na conversa, isso é normal
            if (error.message.includes('already a participant')) {
                addLog('✅ Já é participante da conversa', 'success');
            } else {
                addLog(`Aviso ao juntar-se: ${error.message}`, 'warning');
            }
        }

        // Passo 3: Adicionar participante chat (usuário atual)
        addLog('3/4 - Adicionando participante chat...');
        await addChatParticipant(conversationData.conversationSid, currentUserIdentity);

        // Passo 4: Adicionar participante WhatsApp
        addLog('4/4 - Adicionando participante WhatsApp...');
        const whatsappResponse = await fetch(`/api/conversations/${conversationData.conversationSid}/participants/whatsapp`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ whatsappNumber })
        });

        if (!whatsappResponse.ok) {
            const errorData = await whatsappResponse.json();
            if (whatsappResponse.status === 409) {
                // Participante já existe - sugerir solução
                addLog(`⚠️ ${errorData.error}`, 'warning');
                addLog(`💡 ${errorData.suggestion}`, 'warning');
                throw new Error(errorData.details);
            } else {
                throw new Error(`Erro ao adicionar WhatsApp: ${errorData.error || whatsappResponse.status}`);
            }
        }

        const whatsappData = await whatsappResponse.json();
        addLog(`✅ WhatsApp adicionado: ${whatsappData.address}`, 'success');

        // Configurar eventos da conversa
        setupConversationEvents();

        // Atualizar interface
        updateConversationInfo({
            ...conversationData,
            whatsappNumber: whatsappNumber
        });

        enableChatControls();
        addSystemMessage(`🎉 Conversa iniciada com sucesso! WhatsApp ${whatsappNumber} foi adicionado.`);
        addSystemMessage(`💡 Agora você pode testar o typing indicator digitando no campo abaixo.`);

        addLog('🎉 Conversa WhatsApp configurada com sucesso!', 'success');

    } catch (error) {
        console.error('Erro ao iniciar conversa:', error);
        addLog(`❌ Erro ao iniciar conversa: ${error.message}`, 'error');

        // Resetar estado em caso de erro
        activeConversation = null;

    } finally {
        elements.startConversationBtn.disabled = false;
        elements.startConversationBtn.textContent = '🚀 Iniciar Conversa com WhatsApp';
    }
}



// Adicionar participante chat via API
async function addChatParticipant(conversationSid, identity) {
    try {
        const response = await fetch(`/api/conversations/${conversationSid}/participants/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ identity })
        });

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog(`Participante chat adicionado: ${data.identity}`, 'success');
        return data;

    } catch (error) {
        console.error('Erro ao adicionar participante chat:', error);
        addLog(`Erro ao adicionar participante chat: ${error.message}`, 'error');
        throw error;
    }
}

// Enviar mensagem
async function handleSendMessage() {
    if (!activeConversation) {
        addLog('Nenhuma conversa ativa', 'error');
        return;
    }

    const messageText = elements.messageInput.value.trim();
    
    if (!messageText) {
        return;
    }

    try {
        await activeConversation.sendMessage(messageText);
        elements.messageInput.value = '';
        addLog('Mensagem enviada', 'success');

    } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        addLog(`Erro ao enviar mensagem: ${error.message}`, 'error');
    }
}

// Manipular tecla Enter no input de mensagem
function handleMessageKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSendMessage();
    }
}

// Manipular typing indicator
function handleTyping() {
    if (!activeConversation) {
        return;
    }

    // Enviar typing indicator
    try {
        activeConversation.typing();
        addLog('Typing indicator enviado', 'success');
    } catch (error) {
        console.error('Erro ao enviar typing indicator:', error);
        addLog(`Erro ao enviar typing indicator: ${error.message}`, 'error');
    }
}

// Configurar eventos da conversa
function setupConversationEvents() {
    if (!activeConversation) return;

    // Mensagens
    activeConversation.on('messageAdded', handleMessageAdded);
    
    // Typing indicators
    activeConversation.on('typingStarted', handleTypingStarted);
    activeConversation.on('typingEnded', handleTypingEnded);
    
    // Participantes
    activeConversation.on('participantJoined', handleParticipantJoined);
    activeConversation.on('participantLeft', handleParticipantLeft);

    addLog('Event listeners da conversa configurados', 'success');
}

// Manipular nova mensagem
function handleMessageAdded(message) {
    addLog(`Nova mensagem de ${message.author || 'Sistema'}: ${message.body}`, 'success');
    
    const isOwnMessage = message.author === currentUserIdentity;
    addMessage(message.body, isOwnMessage ? 'sent' : 'received', message.author, message.dateCreated);
}

// Manipular início de digitação
function handleTypingStarted(participant) {
    if (participant.identity !== currentUserIdentity) {
        addLog(`${participant.identity || 'Alguém'} começou a digitar`, 'warning');
        showTypingIndicator(participant.identity || 'Alguém');
    }
}

// Manipular fim de digitação
function handleTypingEnded(participant) {
    if (participant.identity !== currentUserIdentity) {
        addLog(`${participant.identity || 'Alguém'} parou de digitar`, 'warning');
        hideTypingIndicator();
    }
}

// Manipular participante entrando
function handleParticipantJoined(participant) {
    addLog(`Participante entrou: ${participant.identity || participant.address}`, 'success');
    addSystemMessage(`${participant.identity || participant.address} entrou na conversa`);
}

// Manipular participante saindo
function handleParticipantLeft(participant) {
    addLog(`Participante saiu: ${participant.identity || participant.address}`, 'warning');
    addSystemMessage(`${participant.identity || participant.address} saiu da conversa`);
}

// Manipular mudança de estado de conexão
function handleConnectionStateChanged(state) {
    addLog(`Estado de conexão: ${state}`, 'warning');
    
    switch (state) {
        case 'connected':
            updateConnectionStatus('online', 'Conectado');
            break;
        case 'connecting':
            updateConnectionStatus('connecting', 'Conectando...');
            break;
        case 'disconnected':
            updateConnectionStatus('offline', 'Desconectado');
            break;
        case 'denied':
            updateConnectionStatus('offline', 'Conexão negada');
            break;
    }
}

// Manipular conversa juntada
function handleConversationJoined(conversation) {
    addLog(`Juntou-se à conversa: ${conversation.friendlyName}`, 'success');
}

// Manipular conversa deixada
function handleConversationLeft(conversation) {
    addLog(`Deixou a conversa: ${conversation.friendlyName}`, 'warning');
}

// Utilitários de UI
function updateConnectionStatus(status, text) {
    const indicator = elements.connectionStatus.querySelector('.status-indicator');
    const statusText = elements.connectionStatus.querySelector('.status-text');
    
    indicator.className = `status-indicator ${status}`;
    statusText.textContent = text;
}

function updateConversationInfo(conversationData) {
    elements.conversationSid.textContent = conversationData.conversationSid;
    elements.conversationFriendlyName.textContent = conversationData.friendlyName;
    if (conversationData.whatsappNumber) {
        elements.conversationWhatsApp.textContent = conversationData.whatsappNumber;
    }
    elements.conversationInfo.style.display = 'block';
}

function enableConversationControls() {
    elements.startConversationBtn.disabled = false;
}

function enableChatControls() {
    elements.messageInput.disabled = false;
    elements.sendBtn.disabled = false;
    elements.messageInput.focus();
}

function addMessage(text, type, author, timestamp) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    const messageContent = document.createElement('p');
    messageContent.textContent = text;
    messageDiv.appendChild(messageContent);
    
    if (author && timestamp) {
        const metaDiv = document.createElement('div');
        metaDiv.className = 'message-meta';
        metaDiv.textContent = `${author} - ${new Date(timestamp).toLocaleTimeString()}`;
        messageDiv.appendChild(metaDiv);
    }
    
    elements.messagesContainer.appendChild(messageDiv);
    elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
}

function addSystemMessage(text) {
    addMessage(text, 'system');
}

function showTypingIndicator(user) {
    const typingText = elements.typingIndicator.querySelector('.typing-text');
    typingText.textContent = `${user} está digitando...`;
    elements.typingIndicator.style.display = 'flex';
}

function hideTypingIndicator() {
    elements.typingIndicator.style.display = 'none';
}

function addLog(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    
    const timestamp = document.createElement('span');
    timestamp.className = 'timestamp';
    timestamp.textContent = `[${new Date().toLocaleTimeString()}]`;
    
    const logMessage = document.createElement('span');
    logMessage.className = 'log-message';
    logMessage.textContent = message;
    
    logEntry.appendChild(timestamp);
    logEntry.appendChild(logMessage);
    
    elements.logsContainer.appendChild(logEntry);
    elements.logsContainer.scrollTop = elements.logsContainer.scrollHeight;
}

function clearLogs() {
    elements.logsContainer.innerHTML = '';
    addLog('Logs limpos', 'success');
}
