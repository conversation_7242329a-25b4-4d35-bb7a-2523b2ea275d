t=2025-06-04T08:57:21-0300 lvl=info msg="no configuration paths supplied"
t=2025-06-04T08:57:21-0300 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-06-04T08:57:21-0300 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-06-04T08:57:21-0300 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-06-04T08:57:21-0300 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="authentication failed: The account \"<EMAIL>\" may not start an ngrok agent session until the admin's email address is verified. Verify your email at https://dashboard.ngrok.com/user/settings\r\n\r\nERR_NGROK_123\r\n"
t=2025-06-04T08:57:21-0300 lvl=eror msg="session closing" obj=tunnels.session err="authentication failed: The account \"<EMAIL>\" may not start an ngrok agent session until the admin's email address is verified. Verify your email at https://dashboard.ngrok.com/user/settings\r\n\r\nERR_NGROK_123\r\n"
t=2025-06-04T08:57:21-0300 lvl=info msg="received stop request" obj=app stopReq="{err:{Remote:true Inner:{Inner:0xc0000574d0}} restart:false}"
t=2025-06-04T08:57:21-0300 lvl=eror msg="terminating with error" obj=app err="authentication failed: The account \"<EMAIL>\" may not start an ngrok agent session until the admin's email address is verified. Verify your email at https://dashboard.ngrok.com/user/settings\r\n\r\nERR_NGROK_123\r\n"
t=2025-06-04T08:57:21-0300 lvl=warn msg="failed to check for update" obj=updater err="Post \"https://update.equinox.io/check\": context canceled"
t=2025-06-04T08:57:21-0300 lvl=info msg="no more state changes" obj=tunnels.session
t=2025-06-04T08:57:21-0300 lvl=crit msg="command failed" err="authentication failed: The account \"<EMAIL>\" may not start an ngrok agent session until the admin's email address is verified. Verify your email at https://dashboard.ngrok.com/user/settings\r\n\r\nERR_NGROK_123\r\n"
http - start an HTTP tunnel

USAGE:
  ngrok http [address:port | port] [flags]

AUTHOR:
  ngrok - <<EMAIL>>

COMMANDS: 
  config          update or migrate ngrok's configuration file
  http            start an HTTP tunnel
  tcp             start a TCP tunnel
  tunnel          start a tunnel for use with a tunnel-group backend

EXAMPLES: 
  ngrok http 80                                                 # secure public URL for port 80 web server
  ngrok http --domain baz.ngrok.dev 8080                        # port 8080 available at baz.ngrok.dev
  ngrok tcp 22                                                  # tunnel arbitrary TCP traffic to port 22
  ngrok http 80 --oauth=google --oauth-allow-email=<EMAIL>  # secure your app with oauth

Paid Features: 
  ngrok http 80 --domain mydomain.com                           # run ngrok with your own custom domain
  ngrok http 80 --allow-cidr 2600:8c00::a03c:91ee:fe69:9695/32  # run ngrok with IP policy restrictions
  Upgrade your account at https://dashboard.ngrok.com/billing/subscription to access paid features

Upgrade your account at https://dashboard.ngrok.com/billing/subscription to access paid features

Flags:
  -h, --help      help for ngrok

Use "ngrok [command] --help" for more information about a command.

ERROR:  authentication failed: The account "<EMAIL>" may not start an ngrok agent session until the admin's email address is verified. Verify your email at https://dashboard.ngrok.com/user/settings
ERROR:  
ERROR:  ERR_NGROK_123
ERROR:  
