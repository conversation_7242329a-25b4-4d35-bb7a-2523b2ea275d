# 🌐 Configuração de Webhooks - <PERSON><PERSON><PERSON> WhatsApp

Este guia explica como configurar os webhooks da Twilio para que as mensagens do WhatsApp cheguem na sua aplicação e o typing indicator funcione corretamente.

## 🎯 Problema a Resolver

1. **Mensagens do WhatsApp → Aplicação**: Configurar webhook para receber mensagens
2. **Aplicação → WhatsApp**: Garantir que mensagens sejam entregues
3. **Typing Indicator**: Configurar eventos de digitação

## 🛠️ Pré-requisitos

### 1. Instalar ngrok (Túnel Público)

```bash
# Instalar ngrok no macOS
brew install ngrok/ngrok/ngrok

# Ou baixar de: https://ngrok.com/
```

### 2. Criar conta no ngrok (gratuita)

1. Acesse: https://ngrok.com/
2. Crie uma conta
3. Obtenha seu authtoken
4. Configure: `ngrok config add-authtoken SEU_TOKEN`

## 🚀 Configuração Rápida

### Passo 1: Iniciar o Servidor

```bash
# Terminal 1: Iniciar aplicação
node server.js
```

### Passo 2: Criar Túnel Público

```bash
# Terminal 2: Executar script automático
./setup-ngrok.sh
```

O script irá:
- ✅ Verificar se ngrok está instalado
- ✅ Verificar se o servidor está rodando
- ✅ Criar túnel público
- ✅ Gerar URLs dos webhooks
- ✅ Salvar URLs em arquivo
- ✅ Monitorar logs em tempo real

### Passo 3: Configurar Webhooks na Twilio

#### 3.1 WhatsApp Sandbox

1. **Acesse**: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
2. **Configure**:
   - **When a message comes in**: `https://SEU_NGROK_URL.ngrok.io/webhooks/whatsapp/incoming`
   - **Status callback URL**: `https://SEU_NGROK_URL.ngrok.io/webhooks/whatsapp/status`

#### 3.2 Conversations Service

1. **Acesse**: https://console.twilio.com/us1/develop/conversations/manage/services
2. **Selecione** seu Conversations Service
3. **Configure**:
   - **Webhook URL**: `https://SEU_NGROK_URL.ngrok.io/webhooks/conversations`
   - **Webhook Method**: POST
   - **Events**: Marque todos (especialmente `onMessageAdded`, `onTypingStarted`, `onTypingEnded`)

## 📋 URLs dos Webhooks

Após executar `./setup-ngrok.sh`, você terá:

```
🔗 WhatsApp Incoming: https://abc123.ngrok.io/webhooks/whatsapp/incoming
🔗 WhatsApp Status:   https://abc123.ngrok.io/webhooks/whatsapp/status  
🔗 Conversations:     https://abc123.ngrok.io/webhooks/conversations
🔗 Teste:             https://abc123.ngrok.io/webhooks/test
🔗 Logs:              https://abc123.ngrok.io/webhooks/logs
```

## 🧪 Testando a Configuração

### 1. Teste Básico

```bash
# Testar se webhooks estão funcionando
curl https://SEU_NGROK_URL.ngrok.io/webhooks/test
```

### 2. Teste WhatsApp

1. **Envie mensagem** do seu WhatsApp para o número sandbox
2. **Verifique logs**:
   ```bash
   curl https://SEU_NGROK_URL.ngrok.io/webhooks/logs
   ```
3. **Deve aparecer** na aplicação web

### 3. Teste Typing Indicator

1. **Digite** no campo da aplicação web
2. **Verifique** no WhatsApp se aparece "digitando..."
3. **Digite** no WhatsApp
4. **Verifique** logs para eventos de typing

## 📊 Monitoramento e Logs

### Ver Logs em Tempo Real

```bash
# Logs gerais
tail -f logs/twilio-$(date +%Y-%m-%d).log

# Logs de webhooks
tail -f logs/webhook_received-$(date +%Y-%m-%d).log

# Logs de mensagens
tail -f logs/message_received-$(date +%Y-%m-%d).log
```

### Ver Logs via Web

```bash
# Últimas 24 horas
curl https://SEU_NGROK_URL.ngrok.io/webhooks/logs

# Última 1 hora
curl https://SEU_NGROK_URL.ngrok.io/webhooks/logs?hours=1

# Apenas webhooks
curl https://SEU_NGROK_URL.ngrok.io/webhooks/logs?type=webhook_received
```

## 🔧 Troubleshooting

### Problema: Mensagens não chegam na aplicação

**Verificar**:
1. ✅ ngrok está rodando: `curl http://localhost:4040/api/tunnels`
2. ✅ Webhook configurado na Twilio
3. ✅ URL correta (sem trailing slash)
4. ✅ Logs mostram requisições: `curl .../webhooks/logs`

### Problema: Mensagens não saem da aplicação

**Verificar**:
1. ✅ Conversations Service configurado
2. ✅ Participantes adicionados corretamente
3. ✅ Logs mostram envio: `grep message_sent logs/*.log`

### Problema: Typing indicator não funciona

**Verificar**:
1. ✅ Webhook do Conversations configurado
2. ✅ Eventos `onTypingStarted/Ended` habilitados
3. ✅ Ambos participantes na mesma conversa
4. ✅ Logs mostram eventos: `grep typing_indicator logs/*.log`

## 🔄 Comandos Úteis

```bash
# Parar ngrok
pkill -f "ngrok.*3000"

# Reiniciar túnel
./setup-ngrok.sh

# Ver status do ngrok
curl http://localhost:4040/api/tunnels | jq

# Limpar logs antigos
rm logs/*.log

# Testar webhook específico
curl -X POST https://SEU_NGROK_URL.ngrok.io/webhooks/test
```

## 📱 Fluxo Completo de Teste

1. **Iniciar**: `node server.js`
2. **Túnel**: `./setup-ngrok.sh`
3. **Configurar**: Webhooks na Twilio Console
4. **Conectar**: Na aplicação web
5. **Conectar WhatsApp**: Botão na aplicação
6. **Testar**:
   - Enviar mensagem do WhatsApp → Ver na aplicação
   - Digitar na aplicação → Ver typing no WhatsApp
   - Enviar mensagem da aplicação → Ver no WhatsApp

## 🎉 Resultado Esperado

Após configuração correta:

- ✅ **WhatsApp → Aplicação**: Mensagens aparecem no chat web
- ✅ **Aplicação → WhatsApp**: Mensagens chegam no WhatsApp
- ✅ **Typing Indicator**: Funciona em ambas direções
- ✅ **Logs**: Registram todas as comunicações
- ✅ **Sem erros**: "You said..." não aparece mais

---

## 💡 Dicas Importantes

1. **ngrok gratuito**: URL muda a cada reinicialização
2. **Produção**: Use domínio fixo ou ngrok pago
3. **Logs**: Essenciais para debug
4. **Teste incremental**: Um webhook por vez
5. **Backup**: Salve URLs em arquivo para referência
