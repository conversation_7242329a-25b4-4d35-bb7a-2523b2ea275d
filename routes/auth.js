const express = require('express');
const twilio = require('twilio');
const router = express.Router();

// Inicializar cliente Twilio apenas se as credenciais estiverem disponíveis
let client = null;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
}

/**
 * Gerar token de acesso para o Conversations SDK
 * POST /api/auth/token
 */
router.post('/token', async (req, res) => {
    try {
        const { identity } = req.body;

        if (!identity) {
            return res.status(400).json({
                error: 'Identity é obrigatório'
            });
        }

        // Verificar se as credenciais básicas estão configuradas
        if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
            return res.status(500).json({
                error: 'Credenciais básicas da Twilio não configuradas. Verifique TWILIO_ACCOUNT_SID e TWILIO_AUTH_TOKEN no arquivo .env'
            });
        }

        // Verificar se as credenciais da API estão configuradas
        if (!process.env.TWILIO_API_KEY || !process.env.TWILIO_API_SECRET) {
            return res.status(500).json({
                error: 'Credenciais da API Twilio não configuradas. Verifique TWILIO_API_KEY e TWILIO_API_SECRET no arquivo .env'
            });
        }

        // Criar token de acesso
        const { AccessToken } = twilio.jwt;
        const { ChatGrant } = AccessToken;

        const token = new AccessToken(
            process.env.TWILIO_ACCOUNT_SID,
            process.env.TWILIO_API_KEY,
            process.env.TWILIO_API_SECRET,
            { identity: identity }
        );

        // Adicionar grant para Conversations (usa ChatGrant na versão atual)
        const conversationsGrant = new ChatGrant({
            serviceSid: process.env.TWILIO_CONVERSATIONS_SERVICE_SID
        });

        token.addGrant(conversationsGrant);

        res.json({
            token: token.toJwt(),
            identity: identity,
            serviceSid: process.env.TWILIO_CONVERSATIONS_SERVICE_SID
        });

    } catch (error) {
        console.error('Erro ao gerar token:', error);
        res.status(500).json({ 
            error: 'Erro ao gerar token de acesso',
            details: error.message 
        });
    }
});

/**
 * Verificar configuração da Twilio
 * GET /api/auth/verify
 */
router.get('/verify', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env',
                hasBasicCredentials: !!(process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN),
                hasApiCredentials: !!(process.env.TWILIO_API_KEY && process.env.TWILIO_API_SECRET),
                hasConversationsService: !!process.env.TWILIO_CONVERSATIONS_SERVICE_SID
            });
        }

        // Testar conexão com a Twilio
        const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
        
        res.json({
            status: 'OK',
            accountSid: account.sid,
            accountStatus: account.status,
            friendlyName: account.friendlyName,
            hasConversationsService: !!process.env.TWILIO_CONVERSATIONS_SERVICE_SID,
            hasApiCredentials: !!(process.env.TWILIO_API_KEY && process.env.TWILIO_API_SECRET)
        });

    } catch (error) {
        console.error('Erro ao verificar configuração:', error);
        res.status(500).json({ 
            error: 'Erro ao verificar configuração da Twilio',
            details: error.message 
        });
    }
});

module.exports = router;
