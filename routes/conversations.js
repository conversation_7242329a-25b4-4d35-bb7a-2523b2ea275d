const express = require('express');
const twilio = require('twilio');
const router = express.Router();

// Inicializar cliente Twilio apenas se as credenciais estiverem disponíveis
let client = null;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
}

/**
 * Criar uma nova conversa
 * POST /api/conversations/create
 */
router.post('/create', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { friendlyName, uniqueName } = req.body;
        
        const conversationData = {
            friendlyName: friendlyName || `Conversa ${new Date().toLocaleString()}`,
        };

        if (uniqueName) {
            conversationData.uniqueName = uniqueName;
        }

        // Criar conversa usando o service configurado ou o padrão
        let conversation;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversation = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .create(conversationData);
        } else {
            conversation = await client.conversations.v1
                .conversations
                .create(conversationData);
        }

        res.json({
            conversationSid: conversation.sid,
            friendlyName: conversation.friendlyName,
            uniqueName: conversation.uniqueName,
            dateCreated: conversation.dateCreated
        });

    } catch (error) {
        console.error('Erro ao criar conversa:', error);
        res.status(500).json({ 
            error: 'Erro ao criar conversa',
            details: error.message 
        });
    }
});

/**
 * Adicionar participante WhatsApp à conversa
 * POST /api/conversations/:conversationSid/participants/whatsapp
 */
router.post('/:conversationSid/participants/whatsapp', async (req, res) => {
    try {
        const { conversationSid } = req.params;
        const { whatsappNumber } = req.body;

        if (!whatsappNumber) {
            return res.status(400).json({ 
                error: 'Número do WhatsApp é obrigatório' 
            });
        }

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:') 
            ? whatsappNumber 
            : `whatsapp:${whatsappNumber}`;

        const sandboxNumber = process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886';

        // Adicionar participante
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': sandboxNumber
                });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': sandboxNumber
                });
        }

        res.json({
            participantSid: participant.sid,
            address: participant.messagingBinding?.address,
            proxyAddress: participant.messagingBinding?.proxyAddress,
            dateCreated: participant.dateCreated
        });

    } catch (error) {
        console.error('Erro ao adicionar participante WhatsApp:', error);
        res.status(500).json({ 
            error: 'Erro ao adicionar participante WhatsApp',
            details: error.message 
        });
    }
});

/**
 * Adicionar participante chat à conversa
 * POST /api/conversations/:conversationSid/participants/chat
 */
router.post('/:conversationSid/participants/chat', async (req, res) => {
    try {
        const { conversationSid } = req.params;
        const { identity } = req.body;

        if (!identity) {
            return res.status(400).json({ 
                error: 'Identity é obrigatório' 
            });
        }

        // Adicionar participante
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({ identity: identity });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({ identity: identity });
        }

        res.json({
            participantSid: participant.sid,
            identity: participant.identity,
            dateCreated: participant.dateCreated
        });

    } catch (error) {
        console.error('Erro ao adicionar participante chat:', error);
        res.status(500).json({ 
            error: 'Erro ao adicionar participante chat',
            details: error.message 
        });
    }
});

/**
 * Enviar mensagem para a conversa
 * POST /api/conversations/:conversationSid/messages
 */
router.post('/:conversationSid/messages', async (req, res) => {
    try {
        const { conversationSid } = req.params;
        const { body, author } = req.body;

        if (!body) {
            return res.status(400).json({ 
                error: 'Conteúdo da mensagem é obrigatório' 
            });
        }

        const messageData = { body };
        if (author) {
            messageData.author = author;
        }

        // Enviar mensagem
        let message;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            message = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .messages
                .create(messageData);
        } else {
            message = await client.conversations.v1
                .conversations(conversationSid)
                .messages
                .create(messageData);
        }

        res.json({
            messageSid: message.sid,
            body: message.body,
            author: message.author,
            dateCreated: message.dateCreated
        });

    } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        res.status(500).json({ 
            error: 'Erro ao enviar mensagem',
            details: error.message 
        });
    }
});

/**
 * Listar conversas
 * GET /api/conversations
 */
router.get('/', async (req, res) => {
    try {
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 20 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 20 });
        }

        const conversationList = conversations.map(conv => ({
            sid: conv.sid,
            friendlyName: conv.friendlyName,
            uniqueName: conv.uniqueName,
            dateCreated: conv.dateCreated,
            dateUpdated: conv.dateUpdated
        }));

        res.json({ conversations: conversationList });

    } catch (error) {
        console.error('Erro ao listar conversas:', error);
        res.status(500).json({ 
            error: 'Erro ao listar conversas',
            details: error.message 
        });
    }
});

module.exports = router;
