const express = require('express');
const twilio = require('twilio');
const logger = require('../utils/logger');
const router = express.Router();

// Inicializar cliente Twilio apenas se as credenciais estiverem disponíveis
let client = null;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
}

/**
 * Buscar conversa existente com participante WhatsApp
 * GET /api/conversations/find-by-whatsapp/:whatsappNumber
 */
router.get('/find-by-whatsapp/:whatsappNumber', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { whatsappNumber } = req.params;

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        console.log(`Buscando conversa existente para: ${formattedNumber}`);

        // Listar todas as conversas
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        // Para cada conversa, verificar se tem o participante WhatsApp
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Encontrar participante WhatsApp
                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    // Encontrou conversa existente
                    return res.json({
                        found: true,
                        conversation: {
                            conversationSid: conversation.sid,
                            friendlyName: conversation.friendlyName,
                            uniqueName: conversation.uniqueName,
                            dateCreated: conversation.dateCreated,
                            dateUpdated: conversation.dateUpdated
                        },
                        whatsappParticipant: {
                            participantSid: whatsappParticipant.sid,
                            address: whatsappParticipant.messagingBinding?.address,
                            proxyAddress: whatsappParticipant.messagingBinding?.proxyAddress,
                            dateCreated: whatsappParticipant.dateCreated
                        },
                        participants: participants.map(p => ({
                            sid: p.sid,
                            identity: p.identity,
                            address: p.messagingBinding?.address,
                            type: p.messagingBinding ? 'whatsapp' : 'chat'
                        }))
                    });
                }
            } catch (error) {
                console.warn(`Erro ao processar conversa ${conversation.sid}:`, error.message);
            }
        }

        // Não encontrou conversa existente
        res.json({
            found: false,
            message: 'Nenhuma conversa encontrada com este número WhatsApp',
            whatsappNumber: formattedNumber
        });

    } catch (error) {
        console.error('Erro ao buscar conversa existente:', error);
        res.status(500).json({
            error: 'Erro ao buscar conversa existente',
            details: error.message
        });
    }
});

/**
 * Conectar ou criar conversa com WhatsApp (inteligente)
 * POST /api/conversations/connect-whatsapp
 */
router.post('/connect-whatsapp', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { whatsappNumber, friendlyName, userIdentity } = req.body;

        if (!whatsappNumber) {
            return res.status(400).json({
                error: 'Número do WhatsApp é obrigatório'
            });
        }

        if (!userIdentity) {
            return res.status(400).json({
                error: 'Identity do usuário é obrigatório'
            });
        }

        // Validar formato do número
        if (!whatsappNumber.match(/^\+\d{10,15}$/)) {
            return res.status(400).json({
                error: 'Formato do número WhatsApp inválido. Use o formato: +5511999999999'
            });
        }

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        console.log(`Conectando WhatsApp: ${formattedNumber} para usuário: ${userIdentity}`);

        // Primeiro, verificar se já existe uma conversa com este WhatsApp
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        let existingConversation = null;
        let whatsappParticipantExists = false;
        let chatParticipantExists = false;

        // Procurar conversa existente com o WhatsApp
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Verificar se tem o participante WhatsApp
                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    existingConversation = conversation;
                    whatsappParticipantExists = true;

                    // Verificar se o usuário chat também já está na conversa
                    const chatParticipant = participants.find(p => p.identity === userIdentity);
                    if (chatParticipant) {
                        chatParticipantExists = true;
                    }
                    break;
                }
            } catch (error) {
                console.warn(`Erro ao processar conversa ${conversation.sid}:`, error.message);
            }
        }

        let conversationData;
        let isNewConversation = false;

        if (existingConversation) {
            // Usar conversa existente
            console.log(`Usando conversa existente: ${existingConversation.sid}`);
            conversationData = {
                conversationSid: existingConversation.sid,
                friendlyName: existingConversation.friendlyName,
                uniqueName: existingConversation.uniqueName,
                dateCreated: existingConversation.dateCreated,
                dateUpdated: existingConversation.dateUpdated
            };
        } else {
            // Criar nova conversa
            console.log('Criando nova conversa...');
            isNewConversation = true;

            const conversationPayload = {
                friendlyName: friendlyName || `Conversa WhatsApp ${new Date().toLocaleString()}`
            };

            let newConversation;
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                newConversation = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations
                    .create(conversationPayload);
            } else {
                newConversation = await client.conversations.v1
                    .conversations
                    .create(conversationPayload);
            }

            conversationData = {
                conversationSid: newConversation.sid,
                friendlyName: newConversation.friendlyName,
                uniqueName: newConversation.uniqueName,
                dateCreated: newConversation.dateCreated
            };
        }

        // Adicionar participante chat se não existir
        let chatParticipantData = null;
        if (!chatParticipantExists) {
            console.log(`Adicionando participante chat: ${userIdentity}`);
            try {
                let chatParticipant;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    chatParticipant = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversationData.conversationSid)
                        .participants
                        .create({ identity: userIdentity });
                } else {
                    chatParticipant = await client.conversations.v1
                        .conversations(conversationData.conversationSid)
                        .participants
                        .create({ identity: userIdentity });
                }

                chatParticipantData = {
                    participantSid: chatParticipant.sid,
                    identity: chatParticipant.identity,
                    dateCreated: chatParticipant.dateCreated
                };
            } catch (error) {
                if (error.message.includes('already exists') || error.message.includes('already a participant')) {
                    console.log(`Participante chat ${userIdentity} já existe na conversa`);
                    chatParticipantExists = true;
                } else {
                    throw error;
                }
            }
        } else {
            console.log(`Participante chat ${userIdentity} já existe na conversa`);
        }

        // Adicionar participante WhatsApp se não existir
        let whatsappParticipantData = null;
        if (!whatsappParticipantExists) {
            console.log(`Adicionando participante WhatsApp: ${formattedNumber}`);
            try {
                const sandboxNumber = process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886';

                let whatsappParticipant;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    whatsappParticipant = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversationData.conversationSid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': sandboxNumber
                        });
                } else {
                    whatsappParticipant = await client.conversations.v1
                        .conversations(conversationData.conversationSid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': sandboxNumber
                        });
                }

                whatsappParticipantData = {
                    participantSid: whatsappParticipant.sid,
                    address: whatsappParticipant.messagingBinding?.address,
                    proxyAddress: whatsappParticipant.messagingBinding?.proxyAddress,
                    dateCreated: whatsappParticipant.dateCreated
                };
            } catch (error) {
                if (error.message.includes('already exists') || error.message.includes('already a participant')) {
                    console.log(`Participante WhatsApp ${formattedNumber} já existe na conversa`);
                    whatsappParticipantExists = true;
                } else {
                    throw error;
                }
            }
        } else {
            console.log(`Participante WhatsApp ${formattedNumber} já existe na conversa`);
        }

        // Resposta de sucesso
        res.json({
            success: true,
            conversation: conversationData,
            isNewConversation,
            participants: {
                chat: {
                    exists: chatParticipantExists,
                    data: chatParticipantData,
                    identity: userIdentity
                },
                whatsapp: {
                    exists: whatsappParticipantExists,
                    data: whatsappParticipantData,
                    address: formattedNumber
                }
            },
            message: existingConversation
                ? `Conectado à conversa existente: ${existingConversation.friendlyName}`
                : `Nova conversa criada: ${conversationData.friendlyName}`
        });

    } catch (error) {
        console.error('Erro ao conectar conversa WhatsApp:', error);
        res.status(500).json({
            error: 'Erro ao conectar conversa WhatsApp',
            details: error.message
        });
    }
});

/**
 * Criar uma nova conversa
 * POST /api/conversations/create
 */
router.post('/create', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { friendlyName, uniqueName } = req.body;
        
        const conversationData = {
            friendlyName: friendlyName || `Conversa ${new Date().toLocaleString()}`,
        };

        if (uniqueName) {
            conversationData.uniqueName = uniqueName;
        }

        // Criar conversa usando o service configurado ou o padrão
        let conversation;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversation = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .create(conversationData);
        } else {
            conversation = await client.conversations.v1
                .conversations
                .create(conversationData);
        }

        res.json({
            conversationSid: conversation.sid,
            friendlyName: conversation.friendlyName,
            uniqueName: conversation.uniqueName,
            dateCreated: conversation.dateCreated
        });

    } catch (error) {
        console.error('Erro ao criar conversa:', error);
        res.status(500).json({ 
            error: 'Erro ao criar conversa',
            details: error.message 
        });
    }
});

/**
 * Adicionar participante WhatsApp à conversa
 * POST /api/conversations/:conversationSid/participants/whatsapp
 */
router.post('/:conversationSid/participants/whatsapp', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { conversationSid } = req.params;
        const { whatsappNumber } = req.body;

        if (!whatsappNumber) {
            return res.status(400).json({
                error: 'Número do WhatsApp é obrigatório'
            });
        }

        // Validar formato do número
        if (!whatsappNumber.match(/^\+\d{10,15}$/)) {
            return res.status(400).json({
                error: 'Formato do número WhatsApp inválido. Use o formato: +5511999999999'
            });
        }

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        const sandboxNumber = process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886';

        // Adicionar participante
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': sandboxNumber
                });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': sandboxNumber
                });
        }

        res.json({
            participantSid: participant.sid,
            address: participant.messagingBinding?.address,
            proxyAddress: participant.messagingBinding?.proxyAddress,
            dateCreated: participant.dateCreated
        });

    } catch (error) {
        console.error('Erro ao adicionar participante WhatsApp:', error);

        // Tratar erro específico de participante já existente
        if (error.message.includes('already exists')) {
            res.status(409).json({
                error: 'Participante WhatsApp já existe em outra conversa',
                details: 'Este número WhatsApp já está sendo usado em outra conversa. Use um número diferente ou remova-o da conversa anterior.',
                suggestion: 'Tente usar um número WhatsApp diferente'
            });
        } else {
            res.status(500).json({
                error: 'Erro ao adicionar participante WhatsApp',
                details: error.message
            });
        }
    }
});

/**
 * Adicionar participante chat à conversa
 * POST /api/conversations/:conversationSid/participants/chat
 */
router.post('/:conversationSid/participants/chat', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { conversationSid } = req.params;
        const { identity } = req.body;

        if (!identity) {
            return res.status(400).json({
                error: 'Identity é obrigatório'
            });
        }

        // Adicionar participante
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({ identity: identity });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({ identity: identity });
        }

        res.json({
            participantSid: participant.sid,
            identity: participant.identity,
            dateCreated: participant.dateCreated
        });

    } catch (error) {
        console.error('Erro ao adicionar participante chat:', error);
        res.status(500).json({ 
            error: 'Erro ao adicionar participante chat',
            details: error.message 
        });
    }
});

/**
 * Enviar mensagem para a conversa
 * POST /api/conversations/:conversationSid/messages
 */
router.post('/:conversationSid/messages', async (req, res) => {
    try {
        const { conversationSid } = req.params;
        const { body, author } = req.body;

        if (!body) {
            return res.status(400).json({ 
                error: 'Conteúdo da mensagem é obrigatório' 
            });
        }

        const messageData = { body };
        if (author) {
            messageData.author = author;
        }

        // Enviar mensagem
        let message;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            message = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .messages
                .create(messageData);
        } else {
            message = await client.conversations.v1
                .conversations(conversationSid)
                .messages
                .create(messageData);
        }

        // Log da mensagem enviada
        logger.messageSent({
            conversationSid: conversationSid,
            messageSid: message.sid,
            body: message.body,
            author: message.author,
            to: 'conversation'
        });

        res.json({
            messageSid: message.sid,
            body: message.body,
            author: message.author,
            dateCreated: message.dateCreated
        });

    } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        res.status(500).json({ 
            error: 'Erro ao enviar mensagem',
            details: error.message 
        });
    }
});

/**
 * Limpar participantes WhatsApp de todas as conversas (para testes)
 * DELETE /api/conversations/cleanup-whatsapp/:whatsappNumber
 */
router.delete('/cleanup-whatsapp/:whatsappNumber', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { whatsappNumber } = req.params;

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        console.log(`Limpando participante WhatsApp: ${formattedNumber}`);

        // Listar todas as conversas
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 100 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 100 });
        }

        let removedCount = 0;

        // Para cada conversa, verificar se tem o participante WhatsApp
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Encontrar participante WhatsApp
                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    // Remover participante
                    if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                        await client.conversations.v1
                            .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                            .conversations(conversation.sid)
                            .participants(whatsappParticipant.sid)
                            .remove();
                    } else {
                        await client.conversations.v1
                            .conversations(conversation.sid)
                            .participants(whatsappParticipant.sid)
                            .remove();
                    }
                    removedCount++;
                }
            } catch (error) {
                console.warn(`Erro ao processar conversa ${conversation.sid}:`, error.message);
            }
        }

        res.json({
            message: `Participante WhatsApp removido de ${removedCount} conversa(s)`,
            whatsappNumber: formattedNumber,
            conversationsProcessed: conversations.length,
            participantsRemoved: removedCount
        });

    } catch (error) {
        console.error('Erro ao limpar participante WhatsApp:', error);
        res.status(500).json({
            error: 'Erro ao limpar participante WhatsApp',
            details: error.message
        });
    }
});

/**
 * Listar conversas
 * GET /api/conversations
 */
router.get('/', async (req, res) => {
    try {
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 20 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 20 });
        }

        const conversationList = conversations.map(conv => ({
            sid: conv.sid,
            friendlyName: conv.friendlyName,
            uniqueName: conv.uniqueName,
            dateCreated: conv.dateCreated,
            dateUpdated: conv.dateUpdated
        }));

        res.json({ conversations: conversationList });

    } catch (error) {
        console.error('Erro ao listar conversas:', error);
        res.status(500).json({ 
            error: 'Erro ao listar conversas',
            details: error.message 
        });
    }
});

module.exports = router;
