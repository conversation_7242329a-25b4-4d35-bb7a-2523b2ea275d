"use strict";
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const TwiML_1 = __importDefault(require("./TwiML"));
class VoiceResponse extends TwiML_1.default {
    /**
     * <Response> TwiML for Voice
     */
    constructor() {
        super();
        this._propertyName = "response";
    }
    /**
     * Comments in <Response>
     *
     * @param comment - XML Comment
     */
    comment(comment) {
        return this.response.comment(comment);
    }
    /**
     * Comments after <Response>
     *
     * @param comment - XML Comment
     */
    commentAfter(comment) {
        return this.response.commentAfter(comment);
    }
    /**
     * Comments before <Response>
     *
     * @param comment - XML Comment
     */
    commentBefore(comment) {
        return this.response.commentBefore(comment);
    }
    /**
     * <Connect> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    connect(attributes) {
        return new VoiceResponse.Connect(this.response.ele("Connect", attributes));
    }
    dial(attributes, number) {
        if (typeof attributes === "string") {
            number = attributes;
            attributes = {};
        }
        return new VoiceResponse.Dial(this.response.ele("Dial", attributes, number));
    }
    /**
     * <Echo> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    echo(attributes) {
        return new VoiceResponse.Echo(this.response.ele("Echo", attributes));
    }
    enqueue(attributes, name) {
        if (typeof attributes === "string") {
            name = attributes;
            attributes = {};
        }
        return new VoiceResponse.Enqueue(this.response.ele("Enqueue", attributes, name));
    }
    /**
     * <Gather> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    gather(attributes) {
        return new VoiceResponse.Gather(this.response.ele("Gather", attributes));
    }
    /**
     * <Hangup> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    hangup(attributes) {
        return new VoiceResponse.Hangup(this.response.ele("Hangup", attributes));
    }
    /**
     * <Leave> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    leave(attributes) {
        return new VoiceResponse.Leave(this.response.ele("Leave", attributes));
    }
    /**
     * <Pause> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    pause(attributes) {
        return new VoiceResponse.Pause(this.response.ele("Pause", attributes));
    }
    /**
     * <Pay> Twiml Verb
     *
     * @param attributes - TwiML attributes
     */
    pay(attributes) {
        return new VoiceResponse.Pay(this.response.ele("Pay", attributes));
    }
    play(attributes, url) {
        if (typeof attributes === "string") {
            url = attributes;
            attributes = {};
        }
        return new VoiceResponse.Play(this.response.ele("Play", attributes, url));
    }
    /**
     * <Prompt> Twiml Verb
     *
     * @param attributes - TwiML attributes
     */
    prompt(attributes) {
        return new VoiceResponse.Prompt(this.response.ele("Prompt", attributes));
    }
    queue(attributes, name) {
        if (typeof attributes === "string") {
            name = attributes;
            attributes = {};
        }
        return new VoiceResponse.Queue(this.response.ele("Queue", attributes, name));
    }
    /**
     * <Record> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    record(attributes) {
        return new VoiceResponse.Record(this.response.ele("Record", attributes));
    }
    redirect(attributes, url) {
        if (typeof attributes === "string") {
            url = attributes;
            attributes = {};
        }
        return new VoiceResponse.Redirect(this.response.ele("Redirect", attributes, url));
    }
    /**
     * <Refer> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    refer(attributes) {
        return new VoiceResponse.Refer(this.response.ele("Refer", attributes));
    }
    /**
     * <Reject> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    reject(attributes) {
        return new VoiceResponse.Reject(this.response.ele("Reject", attributes));
    }
    say(attributes, message) {
        if (typeof attributes === "string") {
            message = attributes;
            attributes = {};
        }
        return new VoiceResponse.Say(this.response.ele("Say", attributes, message));
    }
    sms(attributes, message) {
        if (typeof attributes === "string") {
            message = attributes;
            attributes = {};
        }
        return new VoiceResponse.Sms(this.response.ele("Sms", attributes, message));
    }
    /**
     * <Start> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    start(attributes) {
        return new VoiceResponse.Start(this.response.ele("Start", attributes));
    }
    /**
     * <Stop> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    stop(attributes) {
        return new VoiceResponse.Stop(this.response.ele("Stop", attributes));
    }
}
(function (VoiceResponse) {
    class Application extends TwiML_1.default {
        /**
         * <Application> TwiML Noun
         */
        constructor(application) {
            super();
            this.application = application;
            this._propertyName = "application";
        }
        applicationSid(attributes, sid) {
            if (typeof attributes === "string") {
                sid = attributes;
                attributes = {};
            }
            return new VoiceResponse.ApplicationSid(this.application.ele("ApplicationSid", attributes, sid));
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.application.ele("Parameter", attributes));
        }
    }
    VoiceResponse.Application = Application;
    class ApplicationSid extends TwiML_1.default {
        /**
         * <ApplicationSid> TwiML Noun
         */
        constructor(applicationSid) {
            super();
            this.applicationSid = applicationSid;
            this._propertyName = "applicationSid";
        }
    }
    VoiceResponse.ApplicationSid = ApplicationSid;
    class Autopilot extends TwiML_1.default {
        /**
         * <Autopilot> TwiML Noun
         */
        constructor(autopilot) {
            super();
            this.autopilot = autopilot;
            this._propertyName = "autopilot";
        }
    }
    VoiceResponse.Autopilot = Autopilot;
    class Client extends TwiML_1.default {
        /**
         * <Client> TwiML Noun
         */
        constructor(client) {
            super();
            this.client = client;
            this._propertyName = "client";
        }
        identity(attributes, clientIdentity) {
            if (typeof attributes === "string") {
                clientIdentity = attributes;
                attributes = {};
            }
            return new VoiceResponse.Identity(this.client.ele("Identity", attributes, clientIdentity));
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.client.ele("Parameter", attributes));
        }
    }
    VoiceResponse.Client = Client;
    class Conference extends TwiML_1.default {
        /**
         * <Conference> TwiML Noun
         */
        constructor(conference) {
            super();
            this.conference = conference;
            this._propertyName = "conference";
        }
    }
    VoiceResponse.Conference = Conference;
    class Config extends TwiML_1.default {
        /**
         * <Config> TwiML Noun
         */
        constructor(config) {
            super();
            this.config = config;
            this._propertyName = "config";
        }
    }
    VoiceResponse.Config = Config;
    class Connect extends TwiML_1.default {
        /**
         * <Connect> TwiML Verb
         */
        constructor(connect) {
            super();
            this.connect = connect;
            this._propertyName = "connect";
        }
        autopilot(attributes, name) {
            if (typeof attributes === "string") {
                name = attributes;
                attributes = {};
            }
            return new VoiceResponse.Autopilot(this.connect.ele("Autopilot", attributes, name));
        }
        /**
         * <Conversation> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        conversation(attributes) {
            return new VoiceResponse.Conversation(this.connect.ele("Conversation", attributes));
        }
        room(attributes, name) {
            if (typeof attributes === "string") {
                name = attributes;
                attributes = {};
            }
            return new VoiceResponse.Room(this.connect.ele("Room", attributes, name));
        }
        /**
         * <Stream> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        stream(attributes) {
            return new VoiceResponse.Stream(this.connect.ele("Stream", attributes));
        }
        /**
         * <VirtualAgent> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        virtualAgent(attributes) {
            return new VoiceResponse.VirtualAgent(this.connect.ele("VirtualAgent", attributes));
        }
    }
    VoiceResponse.Connect = Connect;
    class Conversation extends TwiML_1.default {
        /**
         * <Conversation> TwiML Noun
         */
        constructor(conversation) {
            super();
            this.conversation = conversation;
            this._propertyName = "conversation";
        }
    }
    VoiceResponse.Conversation = Conversation;
    class Dial extends TwiML_1.default {
        /**
         * <Dial> TwiML Verb
         */
        constructor(dial) {
            super();
            this.dial = dial;
            this._propertyName = "dial";
        }
        application(attributes, applicationSid) {
            if (typeof attributes === "string") {
                applicationSid = attributes;
                attributes = {};
            }
            return new VoiceResponse.Application(this.dial.ele("Application", attributes, applicationSid));
        }
        client(attributes, identity) {
            if (typeof attributes === "string") {
                identity = attributes;
                attributes = {};
            }
            return new VoiceResponse.Client(this.dial.ele("Client", attributes, identity));
        }
        conference(attributes, name) {
            if (typeof attributes === "string") {
                name = attributes;
                attributes = {};
            }
            return new VoiceResponse.Conference(this.dial.ele("Conference", attributes, name));
        }
        number(attributes, phoneNumber) {
            if (typeof attributes === "string") {
                phoneNumber = attributes;
                attributes = {};
            }
            return new VoiceResponse.Number(this.dial.ele("Number", attributes, phoneNumber));
        }
        queue(attributes, name) {
            if (typeof attributes === "string") {
                name = attributes;
                attributes = {};
            }
            return new VoiceResponse.Queue(this.dial.ele("Queue", attributes, name));
        }
        sim(attributes, simSid) {
            if (typeof attributes === "string") {
                simSid = attributes;
                attributes = {};
            }
            return new VoiceResponse.Sim(this.dial.ele("Sim", attributes, simSid));
        }
        sip(attributes, sipUrl) {
            if (typeof attributes === "string") {
                sipUrl = attributes;
                attributes = {};
            }
            return new VoiceResponse.Sip(this.dial.ele("Sip", attributes, sipUrl));
        }
    }
    VoiceResponse.Dial = Dial;
    class Echo extends TwiML_1.default {
        /**
         * <Echo> TwiML Verb
         */
        constructor(echo) {
            super();
            this.echo = echo;
            this._propertyName = "echo";
        }
    }
    VoiceResponse.Echo = Echo;
    class Enqueue extends TwiML_1.default {
        /**
         * <Enqueue> TwiML Noun
         */
        constructor(enqueue) {
            super();
            this.enqueue = enqueue;
            this._propertyName = "enqueue";
        }
        task(attributes, body) {
            if (typeof attributes === "string") {
                body = attributes;
                attributes = {};
            }
            return new VoiceResponse.Task(this.enqueue.ele("Task", attributes, body));
        }
    }
    VoiceResponse.Enqueue = Enqueue;
    class Gather extends TwiML_1.default {
        /**
         * <Gather> TwiML Verb
         */
        constructor(gather) {
            super();
            this.gather = gather;
            this._propertyName = "gather";
        }
        /**
         * <Pause> TwiML Verb
         *
         * @param attributes - TwiML attributes
         */
        pause(attributes) {
            return new VoiceResponse.Pause(this.gather.ele("Pause", attributes));
        }
        play(attributes, url) {
            if (typeof attributes === "string") {
                url = attributes;
                attributes = {};
            }
            return new VoiceResponse.Play(this.gather.ele("Play", attributes, url));
        }
        say(attributes, message) {
            if (typeof attributes === "string") {
                message = attributes;
                attributes = {};
            }
            return new VoiceResponse.Say(this.gather.ele("Say", attributes, message));
        }
    }
    VoiceResponse.Gather = Gather;
    class Hangup extends TwiML_1.default {
        /**
         * <Hangup> TwiML Verb
         */
        constructor(hangup) {
            super();
            this.hangup = hangup;
            this._propertyName = "hangup";
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.hangup.ele("Parameter", attributes));
        }
    }
    VoiceResponse.Hangup = Hangup;
    class Identity extends TwiML_1.default {
        /**
         * <Identity> TwiML Noun
         */
        constructor(identity) {
            super();
            this.identity = identity;
            this._propertyName = "identity";
        }
    }
    VoiceResponse.Identity = Identity;
    class Leave extends TwiML_1.default {
        /**
         * <Leave> TwiML Verb
         */
        constructor(leave) {
            super();
            this.leave = leave;
            this._propertyName = "leave";
        }
    }
    VoiceResponse.Leave = Leave;
    class Number extends TwiML_1.default {
        /**
         * <Number> TwiML Noun
         */
        constructor(number) {
            super();
            this.number = number;
            this._propertyName = "number";
        }
    }
    VoiceResponse.Number = Number;
    class Parameter extends TwiML_1.default {
        /**
         * <Parameter> TwiML Noun
         */
        constructor(parameter) {
            super();
            this.parameter = parameter;
            this._propertyName = "parameter";
        }
    }
    VoiceResponse.Parameter = Parameter;
    class Pause extends TwiML_1.default {
        /**
         * <Pause> TwiML Verb
         */
        constructor(pause) {
            super();
            this.pause = pause;
            this._propertyName = "pause";
        }
    }
    VoiceResponse.Pause = Pause;
    class Pay extends TwiML_1.default {
        /**
         * <Pay> Twiml Verb
         */
        constructor(pay) {
            super();
            this.pay = pay;
            this._propertyName = "pay";
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.pay.ele("Parameter", attributes));
        }
        /**
         * <Prompt> Twiml Verb
         *
         * @param attributes - TwiML attributes
         */
        prompt(attributes) {
            return new VoiceResponse.Prompt(this.pay.ele("Prompt", attributes));
        }
    }
    VoiceResponse.Pay = Pay;
    class Play extends TwiML_1.default {
        /**
         * <Play> TwiML Verb
         */
        constructor(play) {
            super();
            this.play = play;
            this._propertyName = "play";
        }
    }
    VoiceResponse.Play = Play;
    class Prompt extends TwiML_1.default {
        /**
         * <Prompt> Twiml Verb
         */
        constructor(prompt) {
            super();
            this.prompt = prompt;
            this._propertyName = "prompt";
        }
        /**
         * <Pause> TwiML Verb
         *
         * @param attributes - TwiML attributes
         */
        pause(attributes) {
            return new VoiceResponse.Pause(this.prompt.ele("Pause", attributes));
        }
        play(attributes, url) {
            if (typeof attributes === "string") {
                url = attributes;
                attributes = {};
            }
            return new VoiceResponse.Play(this.prompt.ele("Play", attributes, url));
        }
        say(attributes, message) {
            if (typeof attributes === "string") {
                message = attributes;
                attributes = {};
            }
            return new VoiceResponse.Say(this.prompt.ele("Say", attributes, message));
        }
    }
    VoiceResponse.Prompt = Prompt;
    class Queue extends TwiML_1.default {
        /**
         * <Queue> TwiML Noun
         */
        constructor(queue) {
            super();
            this.queue = queue;
            this._propertyName = "queue";
        }
    }
    VoiceResponse.Queue = Queue;
    class Record extends TwiML_1.default {
        /**
         * <Record> TwiML Verb
         */
        constructor(record) {
            super();
            this.record = record;
            this._propertyName = "record";
        }
    }
    VoiceResponse.Record = Record;
    class Redirect extends TwiML_1.default {
        /**
         * <Redirect> TwiML Verb
         */
        constructor(redirect) {
            super();
            this.redirect = redirect;
            this._propertyName = "redirect";
        }
    }
    VoiceResponse.Redirect = Redirect;
    class Refer extends TwiML_1.default {
        /**
         * <Refer> TwiML Verb
         */
        constructor(refer) {
            super();
            this.refer = refer;
            this._propertyName = "refer";
        }
        sip(attributes, sipUrl) {
            if (typeof attributes === "string") {
                sipUrl = attributes;
                attributes = {};
            }
            return new VoiceResponse.ReferSip(this.refer.ele("Sip", attributes, sipUrl));
        }
    }
    VoiceResponse.Refer = Refer;
    class ReferSip extends TwiML_1.default {
        /**
         * <Sip> TwiML Noun used in <Refer>
         */
        constructor(referSip) {
            super();
            this.referSip = referSip;
            this._propertyName = "referSip";
        }
    }
    VoiceResponse.ReferSip = ReferSip;
    class Reject extends TwiML_1.default {
        /**
         * <Reject> TwiML Verb
         */
        constructor(reject) {
            super();
            this.reject = reject;
            this._propertyName = "reject";
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.reject.ele("Parameter", attributes));
        }
    }
    VoiceResponse.Reject = Reject;
    class Room extends TwiML_1.default {
        /**
         * <Room> TwiML Noun
         */
        constructor(room) {
            super();
            this.room = room;
            this._propertyName = "room";
        }
    }
    VoiceResponse.Room = Room;
    class Say extends TwiML_1.default {
        /**
         * <Say> TwiML Verb
         */
        constructor(say) {
            super();
            this.say = say;
            this._propertyName = "say";
        }
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes) {
            return new VoiceResponse.SsmlBreak(this.say.ele("break", attributes));
        }
        emphasis(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlEmphasis(this.say.ele("emphasis", attributes, words));
        }
        lang(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlLang(this.say.ele("lang", attributes, words));
        }
        p(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlP(this.say.ele("p", attributes, words));
        }
        phoneme(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlPhoneme(this.say.ele("phoneme", attributes, words));
        }
        prosody(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlProsody(this.say.ele("prosody", attributes, words));
        }
        s(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlS(this.say.ele("s", attributes, words));
        }
        sayAs(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSayAs(this.say.ele("say-as", attributes, words));
        }
        sub(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSub(this.say.ele("sub", attributes, words));
        }
        w(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlW(this.say.ele("w", attributes, words));
        }
    }
    VoiceResponse.Say = Say;
    class Sim extends TwiML_1.default {
        /**
         * <Sim> TwiML Noun
         */
        constructor(sim) {
            super();
            this.sim = sim;
            this._propertyName = "sim";
        }
    }
    VoiceResponse.Sim = Sim;
    class Sip extends TwiML_1.default {
        /**
         * <Sip> TwiML Noun
         */
        constructor(sip) {
            super();
            this.sip = sip;
            this._propertyName = "sip";
        }
    }
    VoiceResponse.Sip = Sip;
    class Siprec extends TwiML_1.default {
        /**
         * <Siprec> TwiML Noun
         */
        constructor(siprec) {
            super();
            this.siprec = siprec;
            this._propertyName = "siprec";
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.siprec.ele("Parameter", attributes));
        }
    }
    VoiceResponse.Siprec = Siprec;
    class Sms extends TwiML_1.default {
        /**
         * <Sms> TwiML Noun
         */
        constructor(sms) {
            super();
            this.sms = sms;
            this._propertyName = "sms";
        }
    }
    VoiceResponse.Sms = Sms;
    class SsmlBreak extends TwiML_1.default {
        /**
         * Adding a Pause in <Say>
         */
        constructor(ssmlBreak) {
            super();
            this.ssmlBreak = ssmlBreak;
            this._propertyName = "ssmlBreak";
        }
    }
    VoiceResponse.SsmlBreak = SsmlBreak;
    class SsmlEmphasis extends TwiML_1.default {
        /**
         * Emphasizing Words in <Say>
         */
        constructor(ssmlEmphasis) {
            super();
            this.ssmlEmphasis = ssmlEmphasis;
            this._propertyName = "ssmlEmphasis";
        }
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes) {
            return new VoiceResponse.SsmlBreak(this.ssmlEmphasis.ele("break", attributes));
        }
        emphasis(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlEmphasis(this.ssmlEmphasis.ele("emphasis", attributes, words));
        }
        lang(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlLang(this.ssmlEmphasis.ele("lang", attributes, words));
        }
        phoneme(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlPhoneme(this.ssmlEmphasis.ele("phoneme", attributes, words));
        }
        prosody(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlProsody(this.ssmlEmphasis.ele("prosody", attributes, words));
        }
        sayAs(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSayAs(this.ssmlEmphasis.ele("say-as", attributes, words));
        }
        sub(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSub(this.ssmlEmphasis.ele("sub", attributes, words));
        }
        w(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlW(this.ssmlEmphasis.ele("w", attributes, words));
        }
    }
    VoiceResponse.SsmlEmphasis = SsmlEmphasis;
    class SsmlLang extends TwiML_1.default {
        /**
         * Specifying Another Language for Specific Words in <Say>
         */
        constructor(ssmlLang) {
            super();
            this.ssmlLang = ssmlLang;
            this._propertyName = "ssmlLang";
        }
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes) {
            return new VoiceResponse.SsmlBreak(this.ssmlLang.ele("break", attributes));
        }
        emphasis(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlEmphasis(this.ssmlLang.ele("emphasis", attributes, words));
        }
        lang(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlLang(this.ssmlLang.ele("lang", attributes, words));
        }
        p(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlP(this.ssmlLang.ele("p", attributes, words));
        }
        phoneme(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlPhoneme(this.ssmlLang.ele("phoneme", attributes, words));
        }
        prosody(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlProsody(this.ssmlLang.ele("prosody", attributes, words));
        }
        s(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlS(this.ssmlLang.ele("s", attributes, words));
        }
        sayAs(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSayAs(this.ssmlLang.ele("say-as", attributes, words));
        }
        sub(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSub(this.ssmlLang.ele("sub", attributes, words));
        }
        w(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlW(this.ssmlLang.ele("w", attributes, words));
        }
    }
    VoiceResponse.SsmlLang = SsmlLang;
    class SsmlP extends TwiML_1.default {
        /**
         * Adding a Pause Between Paragraphs in <Say>
         */
        constructor(ssmlP) {
            super();
            this.ssmlP = ssmlP;
            this._propertyName = "ssmlP";
        }
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes) {
            return new VoiceResponse.SsmlBreak(this.ssmlP.ele("break", attributes));
        }
        emphasis(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlEmphasis(this.ssmlP.ele("emphasis", attributes, words));
        }
        lang(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlLang(this.ssmlP.ele("lang", attributes, words));
        }
        phoneme(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlPhoneme(this.ssmlP.ele("phoneme", attributes, words));
        }
        prosody(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlProsody(this.ssmlP.ele("prosody", attributes, words));
        }
        s(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlS(this.ssmlP.ele("s", attributes, words));
        }
        sayAs(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSayAs(this.ssmlP.ele("say-as", attributes, words));
        }
        sub(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSub(this.ssmlP.ele("sub", attributes, words));
        }
        w(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlW(this.ssmlP.ele("w", attributes, words));
        }
    }
    VoiceResponse.SsmlP = SsmlP;
    class SsmlPhoneme extends TwiML_1.default {
        /**
         * Using Phonetic Pronunciation in <Say>
         */
        constructor(ssmlPhoneme) {
            super();
            this.ssmlPhoneme = ssmlPhoneme;
            this._propertyName = "ssmlPhoneme";
        }
    }
    VoiceResponse.SsmlPhoneme = SsmlPhoneme;
    class SsmlProsody extends TwiML_1.default {
        /**
         * Controling Volume, Speaking Rate, and Pitch in <Say>
         */
        constructor(ssmlProsody) {
            super();
            this.ssmlProsody = ssmlProsody;
            this._propertyName = "ssmlProsody";
        }
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes) {
            return new VoiceResponse.SsmlBreak(this.ssmlProsody.ele("break", attributes));
        }
        emphasis(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlEmphasis(this.ssmlProsody.ele("emphasis", attributes, words));
        }
        lang(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlLang(this.ssmlProsody.ele("lang", attributes, words));
        }
        p(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlP(this.ssmlProsody.ele("p", attributes, words));
        }
        phoneme(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlPhoneme(this.ssmlProsody.ele("phoneme", attributes, words));
        }
        prosody(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlProsody(this.ssmlProsody.ele("prosody", attributes, words));
        }
        s(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlS(this.ssmlProsody.ele("s", attributes, words));
        }
        sayAs(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSayAs(this.ssmlProsody.ele("say-as", attributes, words));
        }
        sub(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSub(this.ssmlProsody.ele("sub", attributes, words));
        }
        w(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlW(this.ssmlProsody.ele("w", attributes, words));
        }
    }
    VoiceResponse.SsmlProsody = SsmlProsody;
    class SsmlS extends TwiML_1.default {
        /**
         * Adding A Pause Between Sentences in <Say>
         */
        constructor(ssmlS) {
            super();
            this.ssmlS = ssmlS;
            this._propertyName = "ssmlS";
        }
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes) {
            return new VoiceResponse.SsmlBreak(this.ssmlS.ele("break", attributes));
        }
        emphasis(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlEmphasis(this.ssmlS.ele("emphasis", attributes, words));
        }
        lang(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlLang(this.ssmlS.ele("lang", attributes, words));
        }
        phoneme(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlPhoneme(this.ssmlS.ele("phoneme", attributes, words));
        }
        prosody(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlProsody(this.ssmlS.ele("prosody", attributes, words));
        }
        sayAs(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSayAs(this.ssmlS.ele("say-as", attributes, words));
        }
        sub(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSub(this.ssmlS.ele("sub", attributes, words));
        }
        w(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlW(this.ssmlS.ele("w", attributes, words));
        }
    }
    VoiceResponse.SsmlS = SsmlS;
    class SsmlSayAs extends TwiML_1.default {
        /**
         * Controlling How Special Types of Words Are Spoken in <Say>
         */
        constructor(ssmlSayAs) {
            super();
            this.ssmlSayAs = ssmlSayAs;
            this._propertyName = "ssmlSayAs";
        }
    }
    VoiceResponse.SsmlSayAs = SsmlSayAs;
    class SsmlSub extends TwiML_1.default {
        /**
         * Pronouncing Acronyms and Abbreviations in <Say>
         */
        constructor(ssmlSub) {
            super();
            this.ssmlSub = ssmlSub;
            this._propertyName = "ssmlSub";
        }
    }
    VoiceResponse.SsmlSub = SsmlSub;
    class SsmlW extends TwiML_1.default {
        /**
         * Improving Pronunciation by Specifying Parts of Speech in <Say>
         */
        constructor(ssmlW) {
            super();
            this.ssmlW = ssmlW;
            this._propertyName = "ssmlW";
        }
        /**
         * Adding a Pause in <Say>
         *
         * @param attributes - TwiML attributes
         */
        break(attributes) {
            return new VoiceResponse.SsmlBreak(this.ssmlW.ele("break", attributes));
        }
        emphasis(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlEmphasis(this.ssmlW.ele("emphasis", attributes, words));
        }
        phoneme(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlPhoneme(this.ssmlW.ele("phoneme", attributes, words));
        }
        prosody(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlProsody(this.ssmlW.ele("prosody", attributes, words));
        }
        sayAs(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSayAs(this.ssmlW.ele("say-as", attributes, words));
        }
        sub(attributes, words) {
            if (typeof attributes === "string") {
                words = attributes;
                attributes = {};
            }
            return new VoiceResponse.SsmlSub(this.ssmlW.ele("sub", attributes, words));
        }
    }
    VoiceResponse.SsmlW = SsmlW;
    class Start extends TwiML_1.default {
        /**
         * <Start> TwiML Verb
         */
        constructor(start) {
            super();
            this.start = start;
            this._propertyName = "start";
        }
        /**
         * <Siprec> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        siprec(attributes) {
            return new VoiceResponse.Siprec(this.start.ele("Siprec", attributes));
        }
        /**
         * <Stream> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        stream(attributes) {
            return new VoiceResponse.Stream(this.start.ele("Stream", attributes));
        }
    }
    VoiceResponse.Start = Start;
    class Stop extends TwiML_1.default {
        /**
         * <Stop> TwiML Verb
         */
        constructor(stop) {
            super();
            this.stop = stop;
            this._propertyName = "stop";
        }
        /**
         * <Siprec> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        siprec(attributes) {
            return new VoiceResponse.Siprec(this.stop.ele("Siprec", attributes));
        }
        /**
         * <Stream> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        stream(attributes) {
            return new VoiceResponse.Stream(this.stop.ele("Stream", attributes));
        }
    }
    VoiceResponse.Stop = Stop;
    class Stream extends TwiML_1.default {
        /**
         * <Stream> TwiML Noun
         */
        constructor(stream) {
            super();
            this.stream = stream;
            this._propertyName = "stream";
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.stream.ele("Parameter", attributes));
        }
    }
    VoiceResponse.Stream = Stream;
    class Task extends TwiML_1.default {
        /**
         * <Task> TwiML Noun
         */
        constructor(task) {
            super();
            this.task = task;
            this._propertyName = "task";
        }
    }
    VoiceResponse.Task = Task;
    class VirtualAgent extends TwiML_1.default {
        /**
         * <VirtualAgent> TwiML Noun
         */
        constructor(virtualAgent) {
            super();
            this.virtualAgent = virtualAgent;
            this._propertyName = "virtualAgent";
        }
        /**
         * <Config> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        config(attributes) {
            return new VoiceResponse.Config(this.virtualAgent.ele("Config", attributes));
        }
        /**
         * <Parameter> TwiML Noun
         *
         * @param attributes - TwiML attributes
         */
        parameter(attributes) {
            return new VoiceResponse.Parameter(this.virtualAgent.ele("Parameter", attributes));
        }
    }
    VoiceResponse.VirtualAgent = VirtualAgent;
})(VoiceResponse || (VoiceResponse = {}));
module.exports = VoiceResponse;
