"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Voice
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CountryPage = exports.CountryListInstance = exports.CountryInstance = exports.CountryContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const highriskSpecialPrefix_1 = require("./country/highriskSpecialPrefix");
class CountryContextImpl {
    constructor(_version, isoCode) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(isoCode)) {
            throw new Error("Parameter 'isoCode' is not valid.");
        }
        this._solution = { isoCode };
        this._uri = `/DialingPermissions/Countries/${isoCode}`;
    }
    get highriskSpecialPrefixes() {
        this._highriskSpecialPrefixes =
            this._highriskSpecialPrefixes ||
                (0, highriskSpecialPrefix_1.HighriskSpecialPrefixListInstance)(this._version, this._solution.isoCode);
        return this._highriskSpecialPrefixes;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new CountryInstance(operationVersion, payload, instance._solution.isoCode));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CountryContextImpl = CountryContextImpl;
class CountryInstance {
    constructor(_version, payload, isoCode) {
        this._version = _version;
        this.isoCode = payload.iso_code;
        this.name = payload.name;
        this.continent = payload.continent;
        this.countryCodes = payload.country_codes;
        this.lowRiskNumbersEnabled = payload.low_risk_numbers_enabled;
        this.highRiskSpecialNumbersEnabled =
            payload.high_risk_special_numbers_enabled;
        this.highRiskTollfraudNumbersEnabled =
            payload.high_risk_tollfraud_numbers_enabled;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { isoCode: isoCode || this.isoCode };
    }
    get _proxy() {
        this._context =
            this._context ||
                new CountryContextImpl(this._version, this._solution.isoCode);
        return this._context;
    }
    /**
     * Fetch a CountryInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed CountryInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the highriskSpecialPrefixes.
     */
    highriskSpecialPrefixes() {
        return this._proxy.highriskSpecialPrefixes;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            isoCode: this.isoCode,
            name: this.name,
            continent: this.continent,
            countryCodes: this.countryCodes,
            lowRiskNumbersEnabled: this.lowRiskNumbersEnabled,
            highRiskSpecialNumbersEnabled: this.highRiskSpecialNumbersEnabled,
            highRiskTollfraudNumbersEnabled: this.highRiskTollfraudNumbersEnabled,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CountryInstance = CountryInstance;
function CountryListInstance(version) {
    const instance = ((isoCode) => instance.get(isoCode));
    instance.get = function get(isoCode) {
        return new CountryContextImpl(version, isoCode);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/DialingPermissions/Countries`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["isoCode"] !== undefined)
            data["IsoCode"] = params["isoCode"];
        if (params["continent"] !== undefined)
            data["Continent"] = params["continent"];
        if (params["countryCode"] !== undefined)
            data["CountryCode"] = params["countryCode"];
        if (params["lowRiskNumbersEnabled"] !== undefined)
            data["LowRiskNumbersEnabled"] = serialize.bool(params["lowRiskNumbersEnabled"]);
        if (params["highRiskSpecialNumbersEnabled"] !== undefined)
            data["HighRiskSpecialNumbersEnabled"] = serialize.bool(params["highRiskSpecialNumbersEnabled"]);
        if (params["highRiskTollfraudNumbersEnabled"] !== undefined)
            data["HighRiskTollfraudNumbersEnabled"] = serialize.bool(params["highRiskTollfraudNumbersEnabled"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CountryPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new CountryPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.CountryListInstance = CountryListInstance;
class CountryPage extends Page_1.default {
    /**
     * Initialize the CountryPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of CountryInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new CountryInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CountryPage = CountryPage;
