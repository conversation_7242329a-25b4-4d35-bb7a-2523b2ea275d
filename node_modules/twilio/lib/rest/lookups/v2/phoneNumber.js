"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Lookups
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhoneNumberListInstance = exports.PhoneNumberInstance = exports.PhoneNumberContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class PhoneNumberContextImpl {
    constructor(_version, phoneNumber) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(phoneNumber)) {
            throw new Error("Parameter 'phoneNumber' is not valid.");
        }
        this._solution = { phoneNumber };
        this._uri = `/PhoneNumbers/${phoneNumber}`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["fields"] !== undefined)
            data["Fields"] = params["fields"];
        if (params["countryCode"] !== undefined)
            data["CountryCode"] = params["countryCode"];
        if (params["firstName"] !== undefined)
            data["FirstName"] = params["firstName"];
        if (params["lastName"] !== undefined)
            data["LastName"] = params["lastName"];
        if (params["addressLine1"] !== undefined)
            data["AddressLine1"] = params["addressLine1"];
        if (params["addressLine2"] !== undefined)
            data["AddressLine2"] = params["addressLine2"];
        if (params["city"] !== undefined)
            data["City"] = params["city"];
        if (params["state"] !== undefined)
            data["State"] = params["state"];
        if (params["postalCode"] !== undefined)
            data["PostalCode"] = params["postalCode"];
        if (params["addressCountryCode"] !== undefined)
            data["AddressCountryCode"] = params["addressCountryCode"];
        if (params["nationalId"] !== undefined)
            data["NationalId"] = params["nationalId"];
        if (params["dateOfBirth"] !== undefined)
            data["DateOfBirth"] = params["dateOfBirth"];
        if (params["lastVerifiedDate"] !== undefined)
            data["LastVerifiedDate"] = params["lastVerifiedDate"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new PhoneNumberInstance(operationVersion, payload, instance._solution.phoneNumber));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PhoneNumberContextImpl = PhoneNumberContextImpl;
class PhoneNumberInstance {
    constructor(_version, payload, phoneNumber) {
        this._version = _version;
        this.callingCountryCode = payload.calling_country_code;
        this.countryCode = payload.country_code;
        this.phoneNumber = payload.phone_number;
        this.nationalFormat = payload.national_format;
        this.valid = payload.valid;
        this.validationErrors = payload.validation_errors;
        this.callerName = payload.caller_name;
        this.simSwap = payload.sim_swap;
        this.callForwarding = payload.call_forwarding;
        this.lineStatus = payload.line_status;
        this.lineTypeIntelligence = payload.line_type_intelligence;
        this.identityMatch = payload.identity_match;
        this.reassignedNumber = payload.reassigned_number;
        this.smsPumpingRisk = payload.sms_pumping_risk;
        this.phoneNumberQualityScore = payload.phone_number_quality_score;
        this.url = payload.url;
        this._solution = { phoneNumber: phoneNumber || this.phoneNumber };
    }
    get _proxy() {
        this._context =
            this._context ||
                new PhoneNumberContextImpl(this._version, this._solution.phoneNumber);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            callingCountryCode: this.callingCountryCode,
            countryCode: this.countryCode,
            phoneNumber: this.phoneNumber,
            nationalFormat: this.nationalFormat,
            valid: this.valid,
            validationErrors: this.validationErrors,
            callerName: this.callerName,
            simSwap: this.simSwap,
            callForwarding: this.callForwarding,
            lineStatus: this.lineStatus,
            lineTypeIntelligence: this.lineTypeIntelligence,
            identityMatch: this.identityMatch,
            reassignedNumber: this.reassignedNumber,
            smsPumpingRisk: this.smsPumpingRisk,
            phoneNumberQualityScore: this.phoneNumberQualityScore,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PhoneNumberInstance = PhoneNumberInstance;
function PhoneNumberListInstance(version) {
    const instance = ((phoneNumber) => instance.get(phoneNumber));
    instance.get = function get(phoneNumber) {
        return new PhoneNumberContextImpl(version, phoneNumber);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.PhoneNumberListInstance = PhoneNumberListInstance;
