"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookListInstance = exports.WebhookInstance = exports.WebhookContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
class WebhookContextImpl {
    constructor(_version) {
        this._version = _version;
        this._solution = {};
        this._uri = `/Configuration/Webhooks`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new WebhookInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["method"] !== undefined)
            data["Method"] = params["method"];
        if (params["filters"] !== undefined)
            data["Filters"] = serialize.map(params["filters"], (e) => e);
        if (params["preWebhookUrl"] !== undefined)
            data["PreWebhookUrl"] = params["preWebhookUrl"];
        if (params["postWebhookUrl"] !== undefined)
            data["PostWebhookUrl"] = params["postWebhookUrl"];
        if (params["target"] !== undefined)
            data["Target"] = params["target"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WebhookInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WebhookContextImpl = WebhookContextImpl;
class WebhookInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.method = payload.method;
        this.filters = payload.filters;
        this.preWebhookUrl = payload.pre_webhook_url;
        this.postWebhookUrl = payload.post_webhook_url;
        this.target = payload.target;
        this.url = payload.url;
        this._solution = {};
    }
    get _proxy() {
        this._context = this._context || new WebhookContextImpl(this._version);
        return this._context;
    }
    /**
     * Fetch a WebhookInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed WebhookInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            method: this.method,
            filters: this.filters,
            preWebhookUrl: this.preWebhookUrl,
            postWebhookUrl: this.postWebhookUrl,
            target: this.target,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WebhookInstance = WebhookInstance;
function WebhookListInstance(version) {
    const instance = (() => instance.get());
    instance.get = function get() {
        return new WebhookContextImpl(version);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.WebhookListInstance = WebhookListInstance;
