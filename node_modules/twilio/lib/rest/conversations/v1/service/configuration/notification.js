"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationListInstance = exports.NotificationInstance = exports.NotificationContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class NotificationContextImpl {
    constructor(_version, chatServiceSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(chatServiceSid)) {
            throw new Error("Parameter 'chatServiceSid' is not valid.");
        }
        this._solution = { chatServiceSid };
        this._uri = `/Services/${chatServiceSid}/Configuration/Notifications`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new NotificationInstance(operationVersion, payload, instance._solution.chatServiceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["logEnabled"] !== undefined)
            data["LogEnabled"] = serialize.bool(params["logEnabled"]);
        if (params["newMessage.enabled"] !== undefined)
            data["NewMessage.Enabled"] = serialize.bool(params["newMessage.enabled"]);
        if (params["newMessage.template"] !== undefined)
            data["NewMessage.Template"] = params["newMessage.template"];
        if (params["newMessage.sound"] !== undefined)
            data["NewMessage.Sound"] = params["newMessage.sound"];
        if (params["newMessage.badgeCountEnabled"] !== undefined)
            data["NewMessage.BadgeCountEnabled"] = serialize.bool(params["newMessage.badgeCountEnabled"]);
        if (params["addedToConversation.enabled"] !== undefined)
            data["AddedToConversation.Enabled"] = serialize.bool(params["addedToConversation.enabled"]);
        if (params["addedToConversation.template"] !== undefined)
            data["AddedToConversation.Template"] =
                params["addedToConversation.template"];
        if (params["addedToConversation.sound"] !== undefined)
            data["AddedToConversation.Sound"] = params["addedToConversation.sound"];
        if (params["removedFromConversation.enabled"] !== undefined)
            data["RemovedFromConversation.Enabled"] = serialize.bool(params["removedFromConversation.enabled"]);
        if (params["removedFromConversation.template"] !== undefined)
            data["RemovedFromConversation.Template"] =
                params["removedFromConversation.template"];
        if (params["removedFromConversation.sound"] !== undefined)
            data["RemovedFromConversation.Sound"] =
                params["removedFromConversation.sound"];
        if (params["newMessage.withMedia.enabled"] !== undefined)
            data["NewMessage.WithMedia.Enabled"] = serialize.bool(params["newMessage.withMedia.enabled"]);
        if (params["newMessage.withMedia.template"] !== undefined)
            data["NewMessage.WithMedia.Template"] =
                params["newMessage.withMedia.template"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new NotificationInstance(operationVersion, payload, instance._solution.chatServiceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.NotificationContextImpl = NotificationContextImpl;
class NotificationInstance {
    constructor(_version, payload, chatServiceSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.chatServiceSid = payload.chat_service_sid;
        this.newMessage = payload.new_message;
        this.addedToConversation = payload.added_to_conversation;
        this.removedFromConversation = payload.removed_from_conversation;
        this.logEnabled = payload.log_enabled;
        this.url = payload.url;
        this._solution = { chatServiceSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new NotificationContextImpl(this._version, this._solution.chatServiceSid);
        return this._context;
    }
    /**
     * Fetch a NotificationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed NotificationInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            chatServiceSid: this.chatServiceSid,
            newMessage: this.newMessage,
            addedToConversation: this.addedToConversation,
            removedFromConversation: this.removedFromConversation,
            logEnabled: this.logEnabled,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.NotificationInstance = NotificationInstance;
function NotificationListInstance(version, chatServiceSid) {
    if (!(0, utility_1.isValidPathParam)(chatServiceSid)) {
        throw new Error("Parameter 'chatServiceSid' is not valid.");
    }
    const instance = (() => instance.get());
    instance.get = function get() {
        return new NotificationContextImpl(version, chatServiceSid);
    };
    instance._version = version;
    instance._solution = { chatServiceSid };
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.NotificationListInstance = NotificationListInstance;
