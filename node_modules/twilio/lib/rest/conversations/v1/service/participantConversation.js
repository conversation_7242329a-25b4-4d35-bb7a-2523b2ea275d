"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipantConversationPage = exports.ParticipantConversationInstance = exports.ParticipantConversationListInstance = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
function ParticipantConversationListInstance(version, chatServiceSid) {
    if (!(0, utility_1.isValidPathParam)(chatServiceSid)) {
        throw new Error("Parameter 'chatServiceSid' is not valid.");
    }
    const instance = {};
    instance._version = version;
    instance._solution = { chatServiceSid };
    instance._uri = `/Services/${chatServiceSid}/ParticipantConversations`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["identity"] !== undefined)
            data["Identity"] = params["identity"];
        if (params["address"] !== undefined)
            data["Address"] = params["address"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantConversationPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ParticipantConversationPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ParticipantConversationListInstance = ParticipantConversationListInstance;
class ParticipantConversationInstance {
    constructor(_version, payload, chatServiceSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.chatServiceSid = payload.chat_service_sid;
        this.participantSid = payload.participant_sid;
        this.participantUserSid = payload.participant_user_sid;
        this.participantIdentity = payload.participant_identity;
        this.participantMessagingBinding = payload.participant_messaging_binding;
        this.conversationSid = payload.conversation_sid;
        this.conversationUniqueName = payload.conversation_unique_name;
        this.conversationFriendlyName = payload.conversation_friendly_name;
        this.conversationAttributes = payload.conversation_attributes;
        this.conversationDateCreated = deserialize.iso8601DateTime(payload.conversation_date_created);
        this.conversationDateUpdated = deserialize.iso8601DateTime(payload.conversation_date_updated);
        this.conversationCreatedBy = payload.conversation_created_by;
        this.conversationState = payload.conversation_state;
        this.conversationTimers = payload.conversation_timers;
        this.links = payload.links;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            chatServiceSid: this.chatServiceSid,
            participantSid: this.participantSid,
            participantUserSid: this.participantUserSid,
            participantIdentity: this.participantIdentity,
            participantMessagingBinding: this.participantMessagingBinding,
            conversationSid: this.conversationSid,
            conversationUniqueName: this.conversationUniqueName,
            conversationFriendlyName: this.conversationFriendlyName,
            conversationAttributes: this.conversationAttributes,
            conversationDateCreated: this.conversationDateCreated,
            conversationDateUpdated: this.conversationDateUpdated,
            conversationCreatedBy: this.conversationCreatedBy,
            conversationState: this.conversationState,
            conversationTimers: this.conversationTimers,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantConversationInstance = ParticipantConversationInstance;
class ParticipantConversationPage extends Page_1.default {
    /**
     * Initialize the ParticipantConversationPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ParticipantConversationInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ParticipantConversationInstance(this._version, payload, this._solution.chatServiceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantConversationPage = ParticipantConversationPage;
