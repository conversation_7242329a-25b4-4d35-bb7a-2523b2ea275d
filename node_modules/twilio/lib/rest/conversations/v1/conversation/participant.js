"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipantPage = exports.ParticipantListInstance = exports.ParticipantInstance = exports.ParticipantContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class ParticipantContextImpl {
    constructor(_version, conversationSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(conversationSid)) {
            throw new Error("Parameter 'conversationSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { conversationSid, sid };
        this._uri = `/Conversations/${conversationSid}/Participants/${sid}`;
    }
    remove(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        const headers = {};
        if (params["xTwilioWebhookEnabled"] !== undefined)
            headers["X-Twilio-Webhook-Enabled"] = params["xTwilioWebhookEnabled"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            params: data,
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.conversationSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["dateCreated"] !== undefined)
            data["DateCreated"] = serialize.iso8601DateTime(params["dateCreated"]);
        if (params["dateUpdated"] !== undefined)
            data["DateUpdated"] = serialize.iso8601DateTime(params["dateUpdated"]);
        if (params["attributes"] !== undefined)
            data["Attributes"] = params["attributes"];
        if (params["roleSid"] !== undefined)
            data["RoleSid"] = params["roleSid"];
        if (params["messagingBinding.proxyAddress"] !== undefined)
            data["MessagingBinding.ProxyAddress"] =
                params["messagingBinding.proxyAddress"];
        if (params["messagingBinding.projectedAddress"] !== undefined)
            data["MessagingBinding.ProjectedAddress"] =
                params["messagingBinding.projectedAddress"];
        if (params["identity"] !== undefined)
            data["Identity"] = params["identity"];
        if (params["lastReadMessageIndex"] !== undefined)
            data["LastReadMessageIndex"] = params["lastReadMessageIndex"];
        if (params["lastReadTimestamp"] !== undefined)
            data["LastReadTimestamp"] = params["lastReadTimestamp"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["xTwilioWebhookEnabled"] !== undefined)
            headers["X-Twilio-Webhook-Enabled"] = params["xTwilioWebhookEnabled"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.conversationSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantContextImpl = ParticipantContextImpl;
class ParticipantInstance {
    constructor(_version, payload, conversationSid, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.conversationSid = payload.conversation_sid;
        this.sid = payload.sid;
        this.identity = payload.identity;
        this.attributes = payload.attributes;
        this.messagingBinding = payload.messaging_binding;
        this.roleSid = payload.role_sid;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.lastReadMessageIndex = deserialize.integer(payload.last_read_message_index);
        this.lastReadTimestamp = payload.last_read_timestamp;
        this._solution = { conversationSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ParticipantContextImpl(this._version, this._solution.conversationSid, this._solution.sid);
        return this._context;
    }
    remove(params, callback) {
        return this._proxy.remove(params, callback);
    }
    /**
     * Fetch a ParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ParticipantInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            conversationSid: this.conversationSid,
            sid: this.sid,
            identity: this.identity,
            attributes: this.attributes,
            messagingBinding: this.messagingBinding,
            roleSid: this.roleSid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            lastReadMessageIndex: this.lastReadMessageIndex,
            lastReadTimestamp: this.lastReadTimestamp,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantInstance = ParticipantInstance;
function ParticipantListInstance(version, conversationSid) {
    if (!(0, utility_1.isValidPathParam)(conversationSid)) {
        throw new Error("Parameter 'conversationSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new ParticipantContextImpl(version, conversationSid, sid);
    };
    instance._version = version;
    instance._solution = { conversationSid };
    instance._uri = `/Conversations/${conversationSid}/Participants`;
    instance.create = function create(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["identity"] !== undefined)
            data["Identity"] = params["identity"];
        if (params["messagingBinding.address"] !== undefined)
            data["MessagingBinding.Address"] = params["messagingBinding.address"];
        if (params["messagingBinding.proxyAddress"] !== undefined)
            data["MessagingBinding.ProxyAddress"] =
                params["messagingBinding.proxyAddress"];
        if (params["dateCreated"] !== undefined)
            data["DateCreated"] = serialize.iso8601DateTime(params["dateCreated"]);
        if (params["dateUpdated"] !== undefined)
            data["DateUpdated"] = serialize.iso8601DateTime(params["dateUpdated"]);
        if (params["attributes"] !== undefined)
            data["Attributes"] = params["attributes"];
        if (params["messagingBinding.projectedAddress"] !== undefined)
            data["MessagingBinding.ProjectedAddress"] =
                params["messagingBinding.projectedAddress"];
        if (params["roleSid"] !== undefined)
            data["RoleSid"] = params["roleSid"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["xTwilioWebhookEnabled"] !== undefined)
            headers["X-Twilio-Webhook-Enabled"] = params["xTwilioWebhookEnabled"];
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.conversationSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ParticipantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ParticipantListInstance = ParticipantListInstance;
class ParticipantPage extends Page_1.default {
    /**
     * Initialize the ParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ParticipantInstance(this._version, payload, this._solution.conversationSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantPage = ParticipantPage;
