"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const Domain_1 = __importDefault(require("../base/Domain"));
const V2010_1 = __importDefault(require("./api/V2010"));
class ApiBase extends Domain_1.default {
    /**
     * Initialize api domain
     *
     * @param twilio - The twilio client
     */
    constructor(twilio) {
        super(twilio, "https://api.twilio.com");
    }
    get v2010() {
        this._v2010 = this._v2010 || new V2010_1.default(this);
        return this._v2010;
    }
}
module.exports = ApiBase;
