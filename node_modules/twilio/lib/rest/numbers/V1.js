"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const bulkEligibility_1 = require("./v1/bulkEligibility");
const portingBulkPortability_1 = require("./v1/portingBulkPortability");
const portingPortInFetch_1 = require("./v1/portingPortInFetch");
const portingPortability_1 = require("./v1/portingPortability");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Numbers
     *
     * @param domain - The Twilio (Twilio.Numbers) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for bulkEligibilities resource */
    get bulkEligibilities() {
        this._bulkEligibilities =
            this._bulkEligibilities || (0, bulkEligibility_1.BulkEligibilityListInstance)(this);
        return this._bulkEligibilities;
    }
    /** Getter for portingBulkPortabilities resource */
    get portingBulkPortabilities() {
        this._portingBulkPortabilities =
            this._portingBulkPortabilities ||
                (0, portingBulkPortability_1.PortingBulkPortabilityListInstance)(this);
        return this._portingBulkPortabilities;
    }
    /** Getter for portingPortIns resource */
    get portingPortIns() {
        this._portingPortIns =
            this._portingPortIns || (0, portingPortInFetch_1.PortingPortInFetchListInstance)(this);
        return this._portingPortIns;
    }
    /** Getter for portingPortabilities resource */
    get portingPortabilities() {
        this._portingPortabilities =
            this._portingPortabilities || (0, portingPortability_1.PortingPortabilityListInstance)(this);
        return this._portingPortabilities;
    }
}
exports.default = V1;
