"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PortingPortabilityListInstance = exports.PortingPortabilityInstance = exports.PortingPortabilityContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class PortingPortabilityContextImpl {
    constructor(_version, phoneNumber) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(phoneNumber)) {
            throw new Error("Parameter 'phoneNumber' is not valid.");
        }
        this._solution = { phoneNumber };
        this._uri = `/Porting/Portability/PhoneNumber/${phoneNumber}`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["targetAccountSid"] !== undefined)
            data["TargetAccountSid"] = params["targetAccountSid"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new PortingPortabilityInstance(operationVersion, payload, instance._solution.phoneNumber));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PortingPortabilityContextImpl = PortingPortabilityContextImpl;
class PortingPortabilityInstance {
    constructor(_version, payload, phoneNumber) {
        this._version = _version;
        this.phoneNumber = payload.phone_number;
        this.accountSid = payload.account_sid;
        this.portable = payload.portable;
        this.pinAndAccountNumberRequired = payload.pin_and_account_number_required;
        this.notPortableReason = payload.not_portable_reason;
        this.notPortableReasonCode = deserialize.integer(payload.not_portable_reason_code);
        this.numberType = payload.number_type;
        this.country = payload.country;
        this.messagingCarrier = payload.messaging_carrier;
        this.voiceCarrier = payload.voice_carrier;
        this.url = payload.url;
        this._solution = { phoneNumber: phoneNumber || this.phoneNumber };
    }
    get _proxy() {
        this._context =
            this._context ||
                new PortingPortabilityContextImpl(this._version, this._solution.phoneNumber);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            phoneNumber: this.phoneNumber,
            accountSid: this.accountSid,
            portable: this.portable,
            pinAndAccountNumberRequired: this.pinAndAccountNumberRequired,
            notPortableReason: this.notPortableReason,
            notPortableReasonCode: this.notPortableReasonCode,
            numberType: this.numberType,
            country: this.country,
            messagingCarrier: this.messagingCarrier,
            voiceCarrier: this.voiceCarrier,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PortingPortabilityInstance = PortingPortabilityInstance;
function PortingPortabilityListInstance(version) {
    const instance = ((phoneNumber) => instance.get(phoneNumber));
    instance.get = function get(phoneNumber) {
        return new PortingPortabilityContextImpl(version, phoneNumber);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.PortingPortabilityListInstance = PortingPortabilityListInstance;
