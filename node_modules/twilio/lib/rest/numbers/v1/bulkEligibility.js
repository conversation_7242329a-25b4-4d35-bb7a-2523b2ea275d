"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkEligibilityListInstance = exports.BulkEligibilityInstance = exports.BulkEligibilityContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class BulkEligibilityContextImpl {
    constructor(_version, requestId) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(requestId)) {
            throw new Error("Parameter 'requestId' is not valid.");
        }
        this._solution = { requestId };
        this._uri = `/HostedNumber/Eligibility/Bulk/${requestId}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new BulkEligibilityInstance(operationVersion, payload, instance._solution.requestId));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BulkEligibilityContextImpl = BulkEligibilityContextImpl;
class BulkEligibilityInstance {
    constructor(_version, payload, requestId) {
        this._version = _version;
        this.requestId = payload.request_id;
        this.url = payload.url;
        this.results = payload.results;
        this.friendlyName = payload.friendly_name;
        this.status = payload.status;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateCompleted = deserialize.iso8601DateTime(payload.date_completed);
        this._solution = { requestId: requestId || this.requestId };
    }
    get _proxy() {
        this._context =
            this._context ||
                new BulkEligibilityContextImpl(this._version, this._solution.requestId);
        return this._context;
    }
    /**
     * Fetch a BulkEligibilityInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed BulkEligibilityInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            requestId: this.requestId,
            url: this.url,
            results: this.results,
            friendlyName: this.friendlyName,
            status: this.status,
            dateCreated: this.dateCreated,
            dateCompleted: this.dateCompleted,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BulkEligibilityInstance = BulkEligibilityInstance;
function BulkEligibilityListInstance(version) {
    const instance = ((requestId) => instance.get(requestId));
    instance.get = function get(requestId) {
        return new BulkEligibilityContextImpl(version, requestId);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.BulkEligibilityListInstance = BulkEligibilityListInstance;
