"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PortingPortInFetchListInstance = exports.PortingPortInFetchInstance = exports.PortingPortInFetchContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class PortingPortInFetchContextImpl {
    constructor(_version, portInRequestSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(portInRequestSid)) {
            throw new Error("Parameter 'portInRequestSid' is not valid.");
        }
        this._solution = { portInRequestSid };
        this._uri = `/Porting/PortIn/${portInRequestSid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new PortingPortInFetchInstance(operationVersion, payload, instance._solution.portInRequestSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PortingPortInFetchContextImpl = PortingPortInFetchContextImpl;
class PortingPortInFetchInstance {
    constructor(_version, payload, portInRequestSid) {
        this._version = _version;
        this.portInRequestSid = payload.port_in_request_sid;
        this.url = payload.url;
        this.accountSid = payload.account_sid;
        this.notificationEmails = payload.notification_emails;
        this.targetPortInDate = deserialize.iso8601Date(payload.target_port_in_date);
        this.targetPortInTimeRangeStart = payload.target_port_in_time_range_start;
        this.targetPortInTimeRangeEnd = payload.target_port_in_time_range_end;
        this.losingCarrierInformation = payload.losing_carrier_information;
        this.phoneNumbers = payload.phone_numbers;
        this.documents = payload.documents;
        this._solution = {
            portInRequestSid: portInRequestSid || this.portInRequestSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new PortingPortInFetchContextImpl(this._version, this._solution.portInRequestSid);
        return this._context;
    }
    /**
     * Fetch a PortingPortInFetchInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed PortingPortInFetchInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            portInRequestSid: this.portInRequestSid,
            url: this.url,
            accountSid: this.accountSid,
            notificationEmails: this.notificationEmails,
            targetPortInDate: this.targetPortInDate,
            targetPortInTimeRangeStart: this.targetPortInTimeRangeStart,
            targetPortInTimeRangeEnd: this.targetPortInTimeRangeEnd,
            losingCarrierInformation: this.losingCarrierInformation,
            phoneNumbers: this.phoneNumbers,
            documents: this.documents,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.PortingPortInFetchInstance = PortingPortInFetchInstance;
function PortingPortInFetchListInstance(version) {
    const instance = ((portInRequestSid) => instance.get(portInRequestSid));
    instance.get = function get(portInRequestSid) {
        return new PortingPortInFetchContextImpl(version, portInRequestSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.PortingPortInFetchListInstance = PortingPortInFetchListInstance;
