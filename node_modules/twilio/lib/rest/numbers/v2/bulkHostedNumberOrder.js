"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkHostedNumberOrderListInstance = exports.BulkHostedNumberOrderInstance = exports.BulkHostedNumberOrderContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class BulkHostedNumberOrderContextImpl {
    constructor(_version, bulkHostingSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(bulkHostingSid)) {
            throw new Error("Parameter 'bulkHostingSid' is not valid.");
        }
        this._solution = { bulkHostingSid };
        this._uri = `/HostedNumber/Orders/Bulk/${bulkHostingSid}`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["orderStatus"] !== undefined)
            data["OrderStatus"] = params["orderStatus"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new BulkHostedNumberOrderInstance(operationVersion, payload, instance._solution.bulkHostingSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BulkHostedNumberOrderContextImpl = BulkHostedNumberOrderContextImpl;
class BulkHostedNumberOrderInstance {
    constructor(_version, payload, bulkHostingSid) {
        this._version = _version;
        this.bulkHostingSid = payload.bulk_hosting_sid;
        this.requestStatus = payload.request_status;
        this.friendlyName = payload.friendly_name;
        this.notificationEmail = payload.notification_email;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateCompleted = deserialize.iso8601DateTime(payload.date_completed);
        this.url = payload.url;
        this.totalCount = deserialize.integer(payload.total_count);
        this.results = payload.results;
        this._solution = { bulkHostingSid: bulkHostingSid || this.bulkHostingSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new BulkHostedNumberOrderContextImpl(this._version, this._solution.bulkHostingSid);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            bulkHostingSid: this.bulkHostingSid,
            requestStatus: this.requestStatus,
            friendlyName: this.friendlyName,
            notificationEmail: this.notificationEmail,
            dateCreated: this.dateCreated,
            dateCompleted: this.dateCompleted,
            url: this.url,
            totalCount: this.totalCount,
            results: this.results,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BulkHostedNumberOrderInstance = BulkHostedNumberOrderInstance;
function BulkHostedNumberOrderListInstance(version) {
    const instance = ((bulkHostingSid) => instance.get(bulkHostingSid));
    instance.get = function get(bulkHostingSid) {
        return new BulkHostedNumberOrderContextImpl(version, bulkHostingSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.BulkHostedNumberOrderListInstance = BulkHostedNumberOrderListInstance;
