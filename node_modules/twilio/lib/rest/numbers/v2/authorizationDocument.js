"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthorizationDocumentPage = exports.AuthorizationDocumentListInstance = exports.AuthorizationDocumentInstance = exports.AuthorizationDocumentContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const dependentHostedNumberOrder_1 = require("./authorizationDocument/dependentHostedNumberOrder");
class AuthorizationDocumentContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/HostedNumber/AuthorizationDocuments/${sid}`;
    }
    get dependentHostedNumberOrders() {
        this._dependentHostedNumberOrders =
            this._dependentHostedNumberOrders ||
                (0, dependentHostedNumberOrder_1.DependentHostedNumberOrderListInstance)(this._version, this._solution.sid);
        return this._dependentHostedNumberOrders;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new AuthorizationDocumentInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthorizationDocumentContextImpl = AuthorizationDocumentContextImpl;
class AuthorizationDocumentInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.addressSid = payload.address_sid;
        this.status = payload.status;
        this.email = payload.email;
        this.ccEmails = payload.cc_emails;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AuthorizationDocumentContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a AuthorizationDocumentInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a AuthorizationDocumentInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AuthorizationDocumentInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the dependentHostedNumberOrders.
     */
    dependentHostedNumberOrders() {
        return this._proxy.dependentHostedNumberOrders;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            addressSid: this.addressSid,
            status: this.status,
            email: this.email,
            ccEmails: this.ccEmails,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthorizationDocumentInstance = AuthorizationDocumentInstance;
function AuthorizationDocumentListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new AuthorizationDocumentContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/HostedNumber/AuthorizationDocuments`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["addressSid"] === null || params["addressSid"] === undefined) {
            throw new Error("Required parameter \"params['addressSid']\" missing.");
        }
        if (params["email"] === null || params["email"] === undefined) {
            throw new Error("Required parameter \"params['email']\" missing.");
        }
        if (params["contactPhoneNumber"] === null ||
            params["contactPhoneNumber"] === undefined) {
            throw new Error("Required parameter \"params['contactPhoneNumber']\" missing.");
        }
        if (params["hostedNumberOrderSids"] === null ||
            params["hostedNumberOrderSids"] === undefined) {
            throw new Error("Required parameter \"params['hostedNumberOrderSids']\" missing.");
        }
        let data = {};
        data["AddressSid"] = params["addressSid"];
        data["Email"] = params["email"];
        data["ContactPhoneNumber"] = params["contactPhoneNumber"];
        data["HostedNumberOrderSids"] = serialize.map(params["hostedNumberOrderSids"], (e) => e);
        if (params["contactTitle"] !== undefined)
            data["ContactTitle"] = params["contactTitle"];
        if (params["ccEmails"] !== undefined)
            data["CcEmails"] = serialize.map(params["ccEmails"], (e) => e);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AuthorizationDocumentInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["email"] !== undefined)
            data["Email"] = params["email"];
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AuthorizationDocumentPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AuthorizationDocumentPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AuthorizationDocumentListInstance = AuthorizationDocumentListInstance;
class AuthorizationDocumentPage extends Page_1.default {
    /**
     * Initialize the AuthorizationDocumentPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AuthorizationDocumentInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AuthorizationDocumentInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthorizationDocumentPage = AuthorizationDocumentPage;
