"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegulatoryComplianceListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const bundle_1 = require("./regulatoryCompliance/bundle");
const endUser_1 = require("./regulatoryCompliance/endUser");
const endUserType_1 = require("./regulatoryCompliance/endUserType");
const regulation_1 = require("./regulatoryCompliance/regulation");
const supportingDocument_1 = require("./regulatoryCompliance/supportingDocument");
const supportingDocumentType_1 = require("./regulatoryCompliance/supportingDocumentType");
function RegulatoryComplianceListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/RegulatoryCompliance`;
    Object.defineProperty(instance, "bundles", {
        get: function bundles() {
            if (!instance._bundles) {
                instance._bundles = (0, bundle_1.BundleListInstance)(instance._version);
            }
            return instance._bundles;
        },
    });
    Object.defineProperty(instance, "endUsers", {
        get: function endUsers() {
            if (!instance._endUsers) {
                instance._endUsers = (0, endUser_1.EndUserListInstance)(instance._version);
            }
            return instance._endUsers;
        },
    });
    Object.defineProperty(instance, "endUserTypes", {
        get: function endUserTypes() {
            if (!instance._endUserTypes) {
                instance._endUserTypes = (0, endUserType_1.EndUserTypeListInstance)(instance._version);
            }
            return instance._endUserTypes;
        },
    });
    Object.defineProperty(instance, "regulations", {
        get: function regulations() {
            if (!instance._regulations) {
                instance._regulations = (0, regulation_1.RegulationListInstance)(instance._version);
            }
            return instance._regulations;
        },
    });
    Object.defineProperty(instance, "supportingDocuments", {
        get: function supportingDocuments() {
            if (!instance._supportingDocuments) {
                instance._supportingDocuments = (0, supportingDocument_1.SupportingDocumentListInstance)(instance._version);
            }
            return instance._supportingDocuments;
        },
    });
    Object.defineProperty(instance, "supportingDocumentTypes", {
        get: function supportingDocumentTypes() {
            if (!instance._supportingDocumentTypes) {
                instance._supportingDocumentTypes = (0, supportingDocumentType_1.SupportingDocumentTypeListInstance)(instance._version);
            }
            return instance._supportingDocumentTypes;
        },
    });
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.RegulatoryComplianceListInstance = RegulatoryComplianceListInstance;
