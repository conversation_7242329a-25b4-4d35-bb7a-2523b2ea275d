"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BundlePage = exports.BundleListInstance = exports.BundleInstance = exports.BundleContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const bundleCopy_1 = require("./bundle/bundleCopy");
const evaluation_1 = require("./bundle/evaluation");
const itemAssignment_1 = require("./bundle/itemAssignment");
const replaceItems_1 = require("./bundle/replaceItems");
class BundleContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/RegulatoryCompliance/Bundles/${sid}`;
    }
    get bundleCopies() {
        this._bundleCopies =
            this._bundleCopies ||
                (0, bundleCopy_1.BundleCopyListInstance)(this._version, this._solution.sid);
        return this._bundleCopies;
    }
    get evaluations() {
        this._evaluations =
            this._evaluations ||
                (0, evaluation_1.EvaluationListInstance)(this._version, this._solution.sid);
        return this._evaluations;
    }
    get itemAssignments() {
        this._itemAssignments =
            this._itemAssignments ||
                (0, itemAssignment_1.ItemAssignmentListInstance)(this._version, this._solution.sid);
        return this._itemAssignments;
    }
    get replaceItems() {
        this._replaceItems =
            this._replaceItems ||
                (0, replaceItems_1.ReplaceItemsListInstance)(this._version, this._solution.sid);
        return this._replaceItems;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new BundleInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["statusCallback"] !== undefined)
            data["StatusCallback"] = params["statusCallback"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["email"] !== undefined)
            data["Email"] = params["email"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new BundleInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BundleContextImpl = BundleContextImpl;
class BundleInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.regulationSid = payload.regulation_sid;
        this.friendlyName = payload.friendly_name;
        this.status = payload.status;
        this.validUntil = deserialize.iso8601DateTime(payload.valid_until);
        this.email = payload.email;
        this.statusCallback = payload.status_callback;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context || new BundleContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a BundleInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a BundleInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed BundleInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the bundleCopies.
     */
    bundleCopies() {
        return this._proxy.bundleCopies;
    }
    /**
     * Access the evaluations.
     */
    evaluations() {
        return this._proxy.evaluations;
    }
    /**
     * Access the itemAssignments.
     */
    itemAssignments() {
        return this._proxy.itemAssignments;
    }
    /**
     * Access the replaceItems.
     */
    replaceItems() {
        return this._proxy.replaceItems;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            regulationSid: this.regulationSid,
            friendlyName: this.friendlyName,
            status: this.status,
            validUntil: this.validUntil,
            email: this.email,
            statusCallback: this.statusCallback,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BundleInstance = BundleInstance;
function BundleListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new BundleContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/RegulatoryCompliance/Bundles`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["friendlyName"] === null ||
            params["friendlyName"] === undefined) {
            throw new Error("Required parameter \"params['friendlyName']\" missing.");
        }
        if (params["email"] === null || params["email"] === undefined) {
            throw new Error("Required parameter \"params['email']\" missing.");
        }
        let data = {};
        data["FriendlyName"] = params["friendlyName"];
        data["Email"] = params["email"];
        if (params["statusCallback"] !== undefined)
            data["StatusCallback"] = params["statusCallback"];
        if (params["regulationSid"] !== undefined)
            data["RegulationSid"] = params["regulationSid"];
        if (params["isoCountry"] !== undefined)
            data["IsoCountry"] = params["isoCountry"];
        if (params["endUserType"] !== undefined)
            data["EndUserType"] = params["endUserType"];
        if (params["numberType"] !== undefined)
            data["NumberType"] = params["numberType"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new BundleInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["regulationSid"] !== undefined)
            data["RegulationSid"] = params["regulationSid"];
        if (params["isoCountry"] !== undefined)
            data["IsoCountry"] = params["isoCountry"];
        if (params["numberType"] !== undefined)
            data["NumberType"] = params["numberType"];
        if (params["hasValidUntilDate"] !== undefined)
            data["HasValidUntilDate"] = serialize.bool(params["hasValidUntilDate"]);
        if (params["sortBy"] !== undefined)
            data["SortBy"] = params["sortBy"];
        if (params["sortDirection"] !== undefined)
            data["SortDirection"] = params["sortDirection"];
        if (params["validUntilDate"] !== undefined)
            data["ValidUntilDate"] = serialize.iso8601DateTime(params["validUntilDate"]);
        if (params["validUntilDateBefore"] !== undefined)
            data["ValidUntilDate<"] = serialize.iso8601DateTime(params["validUntilDateBefore"]);
        if (params["validUntilDateAfter"] !== undefined)
            data["ValidUntilDate>"] = serialize.iso8601DateTime(params["validUntilDateAfter"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new BundlePage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new BundlePage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.BundleListInstance = BundleListInstance;
class BundlePage extends Page_1.default {
    /**
     * Initialize the BundlePage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of BundleInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new BundleInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BundlePage = BundlePage;
