"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HostedNumberOrderPage = exports.HostedNumberOrderListInstance = exports.HostedNumberOrderInstance = exports.HostedNumberOrderContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class HostedNumberOrderContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/HostedNumber/Orders/${sid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new HostedNumberOrderInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.HostedNumberOrderContextImpl = HostedNumberOrderContextImpl;
class HostedNumberOrderInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.incomingPhoneNumberSid = payload.incoming_phone_number_sid;
        this.addressSid = payload.address_sid;
        this.signingDocumentSid = payload.signing_document_sid;
        this.phoneNumber = payload.phone_number;
        this.capabilities = payload.capabilities;
        this.friendlyName = payload.friendly_name;
        this.status = payload.status;
        this.failureReason = payload.failure_reason;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.email = payload.email;
        this.ccEmails = payload.cc_emails;
        this.url = payload.url;
        this.contactTitle = payload.contact_title;
        this.contactPhoneNumber = payload.contact_phone_number;
        this.bulkHostingRequestSid = payload.bulk_hosting_request_sid;
        this.nextStep = payload.next_step;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new HostedNumberOrderContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a HostedNumberOrderInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a HostedNumberOrderInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed HostedNumberOrderInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            incomingPhoneNumberSid: this.incomingPhoneNumberSid,
            addressSid: this.addressSid,
            signingDocumentSid: this.signingDocumentSid,
            phoneNumber: this.phoneNumber,
            capabilities: this.capabilities,
            friendlyName: this.friendlyName,
            status: this.status,
            failureReason: this.failureReason,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            email: this.email,
            ccEmails: this.ccEmails,
            url: this.url,
            contactTitle: this.contactTitle,
            contactPhoneNumber: this.contactPhoneNumber,
            bulkHostingRequestSid: this.bulkHostingRequestSid,
            nextStep: this.nextStep,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.HostedNumberOrderInstance = HostedNumberOrderInstance;
function HostedNumberOrderListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new HostedNumberOrderContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/HostedNumber/Orders`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["phoneNumber"] === null || params["phoneNumber"] === undefined) {
            throw new Error("Required parameter \"params['phoneNumber']\" missing.");
        }
        if (params["contactPhoneNumber"] === null ||
            params["contactPhoneNumber"] === undefined) {
            throw new Error("Required parameter \"params['contactPhoneNumber']\" missing.");
        }
        if (params["addressSid"] === null || params["addressSid"] === undefined) {
            throw new Error("Required parameter \"params['addressSid']\" missing.");
        }
        if (params["email"] === null || params["email"] === undefined) {
            throw new Error("Required parameter \"params['email']\" missing.");
        }
        let data = {};
        data["PhoneNumber"] = params["phoneNumber"];
        data["ContactPhoneNumber"] = params["contactPhoneNumber"];
        data["AddressSid"] = params["addressSid"];
        data["Email"] = params["email"];
        if (params["accountSid"] !== undefined)
            data["AccountSid"] = params["accountSid"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["ccEmails"] !== undefined)
            data["CcEmails"] = serialize.map(params["ccEmails"], (e) => e);
        if (params["smsUrl"] !== undefined)
            data["SmsUrl"] = params["smsUrl"];
        if (params["smsMethod"] !== undefined)
            data["SmsMethod"] = params["smsMethod"];
        if (params["smsFallbackUrl"] !== undefined)
            data["SmsFallbackUrl"] = params["smsFallbackUrl"];
        if (params["smsCapability"] !== undefined)
            data["SmsCapability"] = serialize.bool(params["smsCapability"]);
        if (params["smsFallbackMethod"] !== undefined)
            data["SmsFallbackMethod"] = params["smsFallbackMethod"];
        if (params["statusCallbackUrl"] !== undefined)
            data["StatusCallbackUrl"] = params["statusCallbackUrl"];
        if (params["statusCallbackMethod"] !== undefined)
            data["StatusCallbackMethod"] = params["statusCallbackMethod"];
        if (params["smsApplicationSid"] !== undefined)
            data["SmsApplicationSid"] = params["smsApplicationSid"];
        if (params["contactTitle"] !== undefined)
            data["ContactTitle"] = params["contactTitle"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new HostedNumberOrderInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["smsCapability"] !== undefined)
            data["SmsCapability"] = serialize.bool(params["smsCapability"]);
        if (params["phoneNumber"] !== undefined)
            data["PhoneNumber"] = params["phoneNumber"];
        if (params["incomingPhoneNumberSid"] !== undefined)
            data["IncomingPhoneNumberSid"] = params["incomingPhoneNumberSid"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new HostedNumberOrderPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new HostedNumberOrderPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.HostedNumberOrderListInstance = HostedNumberOrderListInstance;
class HostedNumberOrderPage extends Page_1.default {
    /**
     * Initialize the HostedNumberOrderPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of HostedNumberOrderInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new HostedNumberOrderInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.HostedNumberOrderPage = HostedNumberOrderPage;
