"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalCampaignInstance = exports.ExternalCampaignListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
function ExternalCampaignListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Services/PreregisteredUsa2p`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["campaignId"] === null || params["campaignId"] === undefined) {
            throw new Error("Required parameter \"params['campaignId']\" missing.");
        }
        if (params["messagingServiceSid"] === null ||
            params["messagingServiceSid"] === undefined) {
            throw new Error("Required parameter \"params['messagingServiceSid']\" missing.");
        }
        let data = {};
        data["CampaignId"] = params["campaignId"];
        data["MessagingServiceSid"] = params["messagingServiceSid"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ExternalCampaignInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ExternalCampaignListInstance = ExternalCampaignListInstance;
class ExternalCampaignInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.campaignId = payload.campaign_id;
        this.messagingServiceSid = payload.messaging_service_sid;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            campaignId: this.campaignId,
            messagingServiceSid: this.messagingServiceSid,
            dateCreated: this.dateCreated,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ExternalCampaignInstance = ExternalCampaignInstance;
