"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainCertsListInstance = exports.DomainCertsInstance = exports.DomainCertsContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class DomainCertsContextImpl {
    constructor(_version, domainSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(domainSid)) {
            throw new Error("Parameter 'domainSid' is not valid.");
        }
        this._solution = { domainSid };
        this._uri = `/LinkShortening/Domains/${domainSid}/Certificate`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new DomainCertsInstance(operationVersion, payload, instance._solution.domainSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["tlsCert"] === null || params["tlsCert"] === undefined) {
            throw new Error("Required parameter \"params['tlsCert']\" missing.");
        }
        let data = {};
        data["TlsCert"] = params["tlsCert"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new DomainCertsInstance(operationVersion, payload, instance._solution.domainSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DomainCertsContextImpl = DomainCertsContextImpl;
class DomainCertsInstance {
    constructor(_version, payload, domainSid) {
        this._version = _version;
        this.domainSid = payload.domain_sid;
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.dateExpires = deserialize.iso8601DateTime(payload.date_expires);
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.domainName = payload.domain_name;
        this.certificateSid = payload.certificate_sid;
        this.url = payload.url;
        this.certInValidation = payload.cert_in_validation;
        this._solution = { domainSid: domainSid || this.domainSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new DomainCertsContextImpl(this._version, this._solution.domainSid);
        return this._context;
    }
    /**
     * Remove a DomainCertsInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a DomainCertsInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed DomainCertsInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            domainSid: this.domainSid,
            dateUpdated: this.dateUpdated,
            dateExpires: this.dateExpires,
            dateCreated: this.dateCreated,
            domainName: this.domainName,
            certificateSid: this.certificateSid,
            url: this.url,
            certInValidation: this.certInValidation,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DomainCertsInstance = DomainCertsInstance;
function DomainCertsListInstance(version) {
    const instance = ((domainSid) => instance.get(domainSid));
    instance.get = function get(domainSid) {
        return new DomainCertsContextImpl(version, domainSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.DomainCertsListInstance = DomainCertsListInstance;
