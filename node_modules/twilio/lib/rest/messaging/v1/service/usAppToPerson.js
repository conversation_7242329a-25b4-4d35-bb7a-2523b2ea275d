"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsAppToPersonPage = exports.UsAppToPersonListInstance = exports.UsAppToPersonInstance = exports.UsAppToPersonContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class UsAppToPersonContextImpl {
    constructor(_version, messagingServiceSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
            throw new Error("Parameter 'messagingServiceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { messagingServiceSid, sid };
        this._uri = `/Services/${messagingServiceSid}/Compliance/Usa2p/${sid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new UsAppToPersonInstance(operationVersion, payload, instance._solution.messagingServiceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["hasEmbeddedLinks"] === null ||
            params["hasEmbeddedLinks"] === undefined) {
            throw new Error("Required parameter \"params['hasEmbeddedLinks']\" missing.");
        }
        if (params["hasEmbeddedPhone"] === null ||
            params["hasEmbeddedPhone"] === undefined) {
            throw new Error("Required parameter \"params['hasEmbeddedPhone']\" missing.");
        }
        if (params["messageSamples"] === null ||
            params["messageSamples"] === undefined) {
            throw new Error("Required parameter \"params['messageSamples']\" missing.");
        }
        if (params["messageFlow"] === null || params["messageFlow"] === undefined) {
            throw new Error("Required parameter \"params['messageFlow']\" missing.");
        }
        if (params["description"] === null || params["description"] === undefined) {
            throw new Error("Required parameter \"params['description']\" missing.");
        }
        if (params["ageGated"] === null || params["ageGated"] === undefined) {
            throw new Error("Required parameter \"params['ageGated']\" missing.");
        }
        if (params["directLending"] === null ||
            params["directLending"] === undefined) {
            throw new Error("Required parameter \"params['directLending']\" missing.");
        }
        let data = {};
        data["HasEmbeddedLinks"] = serialize.bool(params["hasEmbeddedLinks"]);
        data["HasEmbeddedPhone"] = serialize.bool(params["hasEmbeddedPhone"]);
        data["MessageSamples"] = serialize.map(params["messageSamples"], (e) => e);
        data["MessageFlow"] = params["messageFlow"];
        data["Description"] = params["description"];
        data["AgeGated"] = serialize.bool(params["ageGated"]);
        data["DirectLending"] = serialize.bool(params["directLending"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UsAppToPersonInstance(operationVersion, payload, instance._solution.messagingServiceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsAppToPersonContextImpl = UsAppToPersonContextImpl;
class UsAppToPersonInstance {
    constructor(_version, payload, messagingServiceSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.brandRegistrationSid = payload.brand_registration_sid;
        this.messagingServiceSid = payload.messaging_service_sid;
        this.description = payload.description;
        this.messageSamples = payload.message_samples;
        this.usAppToPersonUsecase = payload.us_app_to_person_usecase;
        this.hasEmbeddedLinks = payload.has_embedded_links;
        this.hasEmbeddedPhone = payload.has_embedded_phone;
        this.subscriberOptIn = payload.subscriber_opt_in;
        this.ageGated = payload.age_gated;
        this.directLending = payload.direct_lending;
        this.campaignStatus = payload.campaign_status;
        this.campaignId = payload.campaign_id;
        this.isExternallyRegistered = payload.is_externally_registered;
        this.rateLimits = payload.rate_limits;
        this.messageFlow = payload.message_flow;
        this.optInMessage = payload.opt_in_message;
        this.optOutMessage = payload.opt_out_message;
        this.helpMessage = payload.help_message;
        this.optInKeywords = payload.opt_in_keywords;
        this.optOutKeywords = payload.opt_out_keywords;
        this.helpKeywords = payload.help_keywords;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.mock = payload.mock;
        this.errors = payload.errors;
        this._solution = { messagingServiceSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new UsAppToPersonContextImpl(this._version, this._solution.messagingServiceSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a UsAppToPersonInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a UsAppToPersonInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed UsAppToPersonInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            brandRegistrationSid: this.brandRegistrationSid,
            messagingServiceSid: this.messagingServiceSid,
            description: this.description,
            messageSamples: this.messageSamples,
            usAppToPersonUsecase: this.usAppToPersonUsecase,
            hasEmbeddedLinks: this.hasEmbeddedLinks,
            hasEmbeddedPhone: this.hasEmbeddedPhone,
            subscriberOptIn: this.subscriberOptIn,
            ageGated: this.ageGated,
            directLending: this.directLending,
            campaignStatus: this.campaignStatus,
            campaignId: this.campaignId,
            isExternallyRegistered: this.isExternallyRegistered,
            rateLimits: this.rateLimits,
            messageFlow: this.messageFlow,
            optInMessage: this.optInMessage,
            optOutMessage: this.optOutMessage,
            helpMessage: this.helpMessage,
            optInKeywords: this.optInKeywords,
            optOutKeywords: this.optOutKeywords,
            helpKeywords: this.helpKeywords,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            mock: this.mock,
            errors: this.errors,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsAppToPersonInstance = UsAppToPersonInstance;
function UsAppToPersonListInstance(version, messagingServiceSid) {
    if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
        throw new Error("Parameter 'messagingServiceSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new UsAppToPersonContextImpl(version, messagingServiceSid, sid);
    };
    instance._version = version;
    instance._solution = { messagingServiceSid };
    instance._uri = `/Services/${messagingServiceSid}/Compliance/Usa2p`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["brandRegistrationSid"] === null ||
            params["brandRegistrationSid"] === undefined) {
            throw new Error("Required parameter \"params['brandRegistrationSid']\" missing.");
        }
        if (params["description"] === null || params["description"] === undefined) {
            throw new Error("Required parameter \"params['description']\" missing.");
        }
        if (params["messageFlow"] === null || params["messageFlow"] === undefined) {
            throw new Error("Required parameter \"params['messageFlow']\" missing.");
        }
        if (params["messageSamples"] === null ||
            params["messageSamples"] === undefined) {
            throw new Error("Required parameter \"params['messageSamples']\" missing.");
        }
        if (params["usAppToPersonUsecase"] === null ||
            params["usAppToPersonUsecase"] === undefined) {
            throw new Error("Required parameter \"params['usAppToPersonUsecase']\" missing.");
        }
        if (params["hasEmbeddedLinks"] === null ||
            params["hasEmbeddedLinks"] === undefined) {
            throw new Error("Required parameter \"params['hasEmbeddedLinks']\" missing.");
        }
        if (params["hasEmbeddedPhone"] === null ||
            params["hasEmbeddedPhone"] === undefined) {
            throw new Error("Required parameter \"params['hasEmbeddedPhone']\" missing.");
        }
        let data = {};
        data["BrandRegistrationSid"] = params["brandRegistrationSid"];
        data["Description"] = params["description"];
        data["MessageFlow"] = params["messageFlow"];
        data["MessageSamples"] = serialize.map(params["messageSamples"], (e) => e);
        data["UsAppToPersonUsecase"] = params["usAppToPersonUsecase"];
        data["HasEmbeddedLinks"] = serialize.bool(params["hasEmbeddedLinks"]);
        data["HasEmbeddedPhone"] = serialize.bool(params["hasEmbeddedPhone"]);
        if (params["optInMessage"] !== undefined)
            data["OptInMessage"] = params["optInMessage"];
        if (params["optOutMessage"] !== undefined)
            data["OptOutMessage"] = params["optOutMessage"];
        if (params["helpMessage"] !== undefined)
            data["HelpMessage"] = params["helpMessage"];
        if (params["optInKeywords"] !== undefined)
            data["OptInKeywords"] = serialize.map(params["optInKeywords"], (e) => e);
        if (params["optOutKeywords"] !== undefined)
            data["OptOutKeywords"] = serialize.map(params["optOutKeywords"], (e) => e);
        if (params["helpKeywords"] !== undefined)
            data["HelpKeywords"] = serialize.map(params["helpKeywords"], (e) => e);
        if (params["subscriberOptIn"] !== undefined)
            data["SubscriberOptIn"] = serialize.bool(params["subscriberOptIn"]);
        if (params["ageGated"] !== undefined)
            data["AgeGated"] = serialize.bool(params["ageGated"]);
        if (params["directLending"] !== undefined)
            data["DirectLending"] = serialize.bool(params["directLending"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UsAppToPersonInstance(operationVersion, payload, instance._solution.messagingServiceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UsAppToPersonPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new UsAppToPersonPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UsAppToPersonListInstance = UsAppToPersonListInstance;
class UsAppToPersonPage extends Page_1.default {
    /**
     * Initialize the UsAppToPersonPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of UsAppToPersonInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new UsAppToPersonInstance(this._version, payload, this._solution.messagingServiceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsAppToPersonPage = UsAppToPersonPage;
