"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrandVettingPage = exports.BrandVettingListInstance = exports.BrandVettingInstance = exports.BrandVettingContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class BrandVettingContextImpl {
    constructor(_version, brandSid, brandVettingSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(brandSid)) {
            throw new Error("Parameter 'brandSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(brandVettingSid)) {
            throw new Error("Parameter 'brandVettingSid' is not valid.");
        }
        this._solution = { brandSid, brandVettingSid };
        this._uri = `/a2p/BrandRegistrations/${brandSid}/Vettings/${brandVettingSid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new BrandVettingInstance(operationVersion, payload, instance._solution.brandSid, instance._solution.brandVettingSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BrandVettingContextImpl = BrandVettingContextImpl;
class BrandVettingInstance {
    constructor(_version, payload, brandSid, brandVettingSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.brandSid = payload.brand_sid;
        this.brandVettingSid = payload.brand_vetting_sid;
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.vettingId = payload.vetting_id;
        this.vettingClass = payload.vetting_class;
        this.vettingStatus = payload.vetting_status;
        this.vettingProvider = payload.vetting_provider;
        this.url = payload.url;
        this._solution = {
            brandSid,
            brandVettingSid: brandVettingSid || this.brandVettingSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new BrandVettingContextImpl(this._version, this._solution.brandSid, this._solution.brandVettingSid);
        return this._context;
    }
    /**
     * Fetch a BrandVettingInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed BrandVettingInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            brandSid: this.brandSid,
            brandVettingSid: this.brandVettingSid,
            dateUpdated: this.dateUpdated,
            dateCreated: this.dateCreated,
            vettingId: this.vettingId,
            vettingClass: this.vettingClass,
            vettingStatus: this.vettingStatus,
            vettingProvider: this.vettingProvider,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BrandVettingInstance = BrandVettingInstance;
function BrandVettingListInstance(version, brandSid) {
    if (!(0, utility_1.isValidPathParam)(brandSid)) {
        throw new Error("Parameter 'brandSid' is not valid.");
    }
    const instance = ((brandVettingSid) => instance.get(brandVettingSid));
    instance.get = function get(brandVettingSid) {
        return new BrandVettingContextImpl(version, brandSid, brandVettingSid);
    };
    instance._version = version;
    instance._solution = { brandSid };
    instance._uri = `/a2p/BrandRegistrations/${brandSid}/Vettings`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["vettingProvider"] === null ||
            params["vettingProvider"] === undefined) {
            throw new Error("Required parameter \"params['vettingProvider']\" missing.");
        }
        let data = {};
        data["VettingProvider"] = params["vettingProvider"];
        if (params["vettingId"] !== undefined)
            data["VettingId"] = params["vettingId"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new BrandVettingInstance(operationVersion, payload, instance._solution.brandSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["vettingProvider"] !== undefined)
            data["VettingProvider"] = params["vettingProvider"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new BrandVettingPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new BrandVettingPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.BrandVettingListInstance = BrandVettingListInstance;
class BrandVettingPage extends Page_1.default {
    /**
     * Initialize the BrandVettingPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of BrandVettingInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new BrandVettingInstance(this._version, payload, this._solution.brandSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BrandVettingPage = BrandVettingPage;
