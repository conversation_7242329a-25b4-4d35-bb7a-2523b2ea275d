"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsightsQuestionnairesQuestionPage = exports.InsightsQuestionnairesQuestionListInstance = exports.InsightsQuestionnairesQuestionInstance = exports.InsightsQuestionnairesQuestionContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class InsightsQuestionnairesQuestionContextImpl {
    constructor(_version, questionSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(questionSid)) {
            throw new Error("Parameter 'questionSid' is not valid.");
        }
        this._solution = { questionSid };
        this._uri = `/Insights/QualityManagement/Questions/${questionSid}`;
    }
    remove(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            params: data,
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["allowNa"] === null || params["allowNa"] === undefined) {
            throw new Error("Required parameter \"params['allowNa']\" missing.");
        }
        let data = {};
        data["AllowNa"] = serialize.bool(params["allowNa"]);
        if (params["categorySid"] !== undefined)
            data["CategorySid"] = params["categorySid"];
        if (params["question"] !== undefined)
            data["Question"] = params["question"];
        if (params["description"] !== undefined)
            data["Description"] = params["description"];
        if (params["answerSetId"] !== undefined)
            data["AnswerSetId"] = params["answerSetId"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesQuestionInstance(operationVersion, payload, instance._solution.questionSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesQuestionContextImpl = InsightsQuestionnairesQuestionContextImpl;
class InsightsQuestionnairesQuestionInstance {
    constructor(_version, payload, questionSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.questionSid = payload.question_sid;
        this.question = payload.question;
        this.description = payload.description;
        this.category = payload.category;
        this.answerSetId = payload.answer_set_id;
        this.allowNa = payload.allow_na;
        this.usage = deserialize.integer(payload.usage);
        this.answerSet = payload.answer_set;
        this.url = payload.url;
        this._solution = { questionSid: questionSid || this.questionSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new InsightsQuestionnairesQuestionContextImpl(this._version, this._solution.questionSid);
        return this._context;
    }
    remove(params, callback) {
        return this._proxy.remove(params, callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            questionSid: this.questionSid,
            question: this.question,
            description: this.description,
            category: this.category,
            answerSetId: this.answerSetId,
            allowNa: this.allowNa,
            usage: this.usage,
            answerSet: this.answerSet,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesQuestionInstance = InsightsQuestionnairesQuestionInstance;
function InsightsQuestionnairesQuestionListInstance(version) {
    const instance = ((questionSid) => instance.get(questionSid));
    instance.get = function get(questionSid) {
        return new InsightsQuestionnairesQuestionContextImpl(version, questionSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Insights/QualityManagement/Questions`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["categorySid"] === null || params["categorySid"] === undefined) {
            throw new Error("Required parameter \"params['categorySid']\" missing.");
        }
        if (params["question"] === null || params["question"] === undefined) {
            throw new Error("Required parameter \"params['question']\" missing.");
        }
        if (params["answerSetId"] === null || params["answerSetId"] === undefined) {
            throw new Error("Required parameter \"params['answerSetId']\" missing.");
        }
        if (params["allowNa"] === null || params["allowNa"] === undefined) {
            throw new Error("Required parameter \"params['allowNa']\" missing.");
        }
        let data = {};
        data["CategorySid"] = params["categorySid"];
        data["Question"] = params["question"];
        data["AnswerSetId"] = params["answerSetId"];
        data["AllowNa"] = serialize.bool(params["allowNa"]);
        if (params["description"] !== undefined)
            data["Description"] = params["description"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesQuestionInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["categorySid"] !== undefined)
            data["CategorySid"] = serialize.map(params["categorySid"], (e) => e);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesQuestionPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new InsightsQuestionnairesQuestionPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InsightsQuestionnairesQuestionListInstance = InsightsQuestionnairesQuestionListInstance;
class InsightsQuestionnairesQuestionPage extends Page_1.default {
    /**
     * Initialize the InsightsQuestionnairesQuestionPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of InsightsQuestionnairesQuestionInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new InsightsQuestionnairesQuestionInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesQuestionPage = InsightsQuestionnairesQuestionPage;
