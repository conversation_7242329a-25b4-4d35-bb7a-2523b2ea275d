"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationListInstance = exports.ConfigurationInstance = exports.ConfigurationContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
class ConfigurationContextImpl {
    constructor(_version) {
        this._version = _version;
        this._solution = {};
        this._uri = `/Configuration`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["uiVersion"] !== undefined)
            data["UiVersion"] = params["uiVersion"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ConfigurationInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConfigurationContextImpl = ConfigurationContextImpl;
class ConfigurationInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.attributes = payload.attributes;
        this.status = payload.status;
        this.taskrouterWorkspaceSid = payload.taskrouter_workspace_sid;
        this.taskrouterTargetWorkflowSid = payload.taskrouter_target_workflow_sid;
        this.taskrouterTargetTaskqueueSid = payload.taskrouter_target_taskqueue_sid;
        this.taskrouterTaskqueues = payload.taskrouter_taskqueues;
        this.taskrouterSkills = payload.taskrouter_skills;
        this.taskrouterWorkerChannels = payload.taskrouter_worker_channels;
        this.taskrouterWorkerAttributes = payload.taskrouter_worker_attributes;
        this.taskrouterOfflineActivitySid = payload.taskrouter_offline_activity_sid;
        this.runtimeDomain = payload.runtime_domain;
        this.messagingServiceInstanceSid = payload.messaging_service_instance_sid;
        this.chatServiceInstanceSid = payload.chat_service_instance_sid;
        this.flexServiceInstanceSid = payload.flex_service_instance_sid;
        this.flexInstanceSid = payload.flex_instance_sid;
        this.uiLanguage = payload.ui_language;
        this.uiAttributes = payload.ui_attributes;
        this.uiDependencies = payload.ui_dependencies;
        this.uiVersion = payload.ui_version;
        this.serviceVersion = payload.service_version;
        this.callRecordingEnabled = payload.call_recording_enabled;
        this.callRecordingWebhookUrl = payload.call_recording_webhook_url;
        this.crmEnabled = payload.crm_enabled;
        this.crmType = payload.crm_type;
        this.crmCallbackUrl = payload.crm_callback_url;
        this.crmFallbackUrl = payload.crm_fallback_url;
        this.crmAttributes = payload.crm_attributes;
        this.publicAttributes = payload.public_attributes;
        this.pluginServiceEnabled = payload.plugin_service_enabled;
        this.pluginServiceAttributes = payload.plugin_service_attributes;
        this.integrations = payload.integrations;
        this.outboundCallFlows = payload.outbound_call_flows;
        this.serverlessServiceSids = payload.serverless_service_sids;
        this.queueStatsConfiguration = payload.queue_stats_configuration;
        this.notifications = payload.notifications;
        this.markdown = payload.markdown;
        this.url = payload.url;
        this.flexInsightsHr = payload.flex_insights_hr;
        this.flexInsightsDrilldown = payload.flex_insights_drilldown;
        this.flexUrl = payload.flex_url;
        this.channelConfigs = payload.channel_configs;
        this.debuggerIntegration = payload.debugger_integration;
        this.flexUiStatusReport = payload.flex_ui_status_report;
        this.agentConvEndMethods = payload.agent_conv_end_methods;
        this.citrixVoiceVdi = payload.citrix_voice_vdi;
        this.offlineConfig = payload.offline_config;
        this._solution = {};
    }
    get _proxy() {
        this._context =
            this._context || new ConfigurationContextImpl(this._version);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            attributes: this.attributes,
            status: this.status,
            taskrouterWorkspaceSid: this.taskrouterWorkspaceSid,
            taskrouterTargetWorkflowSid: this.taskrouterTargetWorkflowSid,
            taskrouterTargetTaskqueueSid: this.taskrouterTargetTaskqueueSid,
            taskrouterTaskqueues: this.taskrouterTaskqueues,
            taskrouterSkills: this.taskrouterSkills,
            taskrouterWorkerChannels: this.taskrouterWorkerChannels,
            taskrouterWorkerAttributes: this.taskrouterWorkerAttributes,
            taskrouterOfflineActivitySid: this.taskrouterOfflineActivitySid,
            runtimeDomain: this.runtimeDomain,
            messagingServiceInstanceSid: this.messagingServiceInstanceSid,
            chatServiceInstanceSid: this.chatServiceInstanceSid,
            flexServiceInstanceSid: this.flexServiceInstanceSid,
            flexInstanceSid: this.flexInstanceSid,
            uiLanguage: this.uiLanguage,
            uiAttributes: this.uiAttributes,
            uiDependencies: this.uiDependencies,
            uiVersion: this.uiVersion,
            serviceVersion: this.serviceVersion,
            callRecordingEnabled: this.callRecordingEnabled,
            callRecordingWebhookUrl: this.callRecordingWebhookUrl,
            crmEnabled: this.crmEnabled,
            crmType: this.crmType,
            crmCallbackUrl: this.crmCallbackUrl,
            crmFallbackUrl: this.crmFallbackUrl,
            crmAttributes: this.crmAttributes,
            publicAttributes: this.publicAttributes,
            pluginServiceEnabled: this.pluginServiceEnabled,
            pluginServiceAttributes: this.pluginServiceAttributes,
            integrations: this.integrations,
            outboundCallFlows: this.outboundCallFlows,
            serverlessServiceSids: this.serverlessServiceSids,
            queueStatsConfiguration: this.queueStatsConfiguration,
            notifications: this.notifications,
            markdown: this.markdown,
            url: this.url,
            flexInsightsHr: this.flexInsightsHr,
            flexInsightsDrilldown: this.flexInsightsDrilldown,
            flexUrl: this.flexUrl,
            channelConfigs: this.channelConfigs,
            debuggerIntegration: this.debuggerIntegration,
            flexUiStatusReport: this.flexUiStatusReport,
            agentConvEndMethods: this.agentConvEndMethods,
            citrixVoiceVdi: this.citrixVoiceVdi,
            offlineConfig: this.offlineConfig,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConfigurationInstance = ConfigurationInstance;
function ConfigurationListInstance(version) {
    const instance = (() => instance.get());
    instance.get = function get() {
        return new ConfigurationContextImpl(version);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ConfigurationListInstance = ConfigurationListInstance;
