"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionChannelParticipantPage = exports.InteractionChannelParticipantListInstance = exports.InteractionChannelParticipantInstance = exports.InteractionChannelParticipantContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class InteractionChannelParticipantContextImpl {
    constructor(_version, interactionSid, channelSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(interactionSid)) {
            throw new Error("Parameter 'interactionSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(channelSid)) {
            throw new Error("Parameter 'channelSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { interactionSid, channelSid, sid };
        this._uri = `/Interactions/${interactionSid}/Channels/${channelSid}/Participants/${sid}`;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["status"] === null || params["status"] === undefined) {
            throw new Error("Required parameter \"params['status']\" missing.");
        }
        let data = {};
        data["Status"] = params["status"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelParticipantInstance(operationVersion, payload, instance._solution.interactionSid, instance._solution.channelSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelParticipantContextImpl = InteractionChannelParticipantContextImpl;
class InteractionChannelParticipantInstance {
    constructor(_version, payload, interactionSid, channelSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.type = payload.type;
        this.interactionSid = payload.interaction_sid;
        this.channelSid = payload.channel_sid;
        this.url = payload.url;
        this.routingProperties = payload.routing_properties;
        this._solution = { interactionSid, channelSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new InteractionChannelParticipantContextImpl(this._version, this._solution.interactionSid, this._solution.channelSid, this._solution.sid);
        return this._context;
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            type: this.type,
            interactionSid: this.interactionSid,
            channelSid: this.channelSid,
            url: this.url,
            routingProperties: this.routingProperties,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelParticipantInstance = InteractionChannelParticipantInstance;
function InteractionChannelParticipantListInstance(version, interactionSid, channelSid) {
    if (!(0, utility_1.isValidPathParam)(interactionSid)) {
        throw new Error("Parameter 'interactionSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(channelSid)) {
        throw new Error("Parameter 'channelSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new InteractionChannelParticipantContextImpl(version, interactionSid, channelSid, sid);
    };
    instance._version = version;
    instance._solution = { interactionSid, channelSid };
    instance._uri = `/Interactions/${interactionSid}/Channels/${channelSid}/Participants`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["type"] === null || params["type"] === undefined) {
            throw new Error("Required parameter \"params['type']\" missing.");
        }
        if (params["mediaProperties"] === null ||
            params["mediaProperties"] === undefined) {
            throw new Error("Required parameter \"params['mediaProperties']\" missing.");
        }
        let data = {};
        data["Type"] = params["type"];
        data["MediaProperties"] = serialize.object(params["mediaProperties"]);
        if (params["routingProperties"] !== undefined)
            data["RoutingProperties"] = serialize.object(params["routingProperties"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelParticipantInstance(operationVersion, payload, instance._solution.interactionSid, instance._solution.channelSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelParticipantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new InteractionChannelParticipantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InteractionChannelParticipantListInstance = InteractionChannelParticipantListInstance;
class InteractionChannelParticipantPage extends Page_1.default {
    /**
     * Initialize the InteractionChannelParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of InteractionChannelParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new InteractionChannelParticipantInstance(this._version, payload, this._solution.interactionSid, this._solution.channelSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelParticipantPage = InteractionChannelParticipantPage;
