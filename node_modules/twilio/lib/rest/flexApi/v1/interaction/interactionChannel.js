"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionChannelPage = exports.InteractionChannelListInstance = exports.InteractionChannelInstance = exports.InteractionChannelContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const interactionChannelInvite_1 = require("./interactionChannel/interactionChannelInvite");
const interactionChannelParticipant_1 = require("./interactionChannel/interactionChannelParticipant");
class InteractionChannelContextImpl {
    constructor(_version, interactionSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(interactionSid)) {
            throw new Error("Parameter 'interactionSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { interactionSid, sid };
        this._uri = `/Interactions/${interactionSid}/Channels/${sid}`;
    }
    get invites() {
        this._invites =
            this._invites ||
                (0, interactionChannelInvite_1.InteractionChannelInviteListInstance)(this._version, this._solution.interactionSid, this._solution.sid);
        return this._invites;
    }
    get participants() {
        this._participants =
            this._participants ||
                (0, interactionChannelParticipant_1.InteractionChannelParticipantListInstance)(this._version, this._solution.interactionSid, this._solution.sid);
        return this._participants;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelInstance(operationVersion, payload, instance._solution.interactionSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["status"] === null || params["status"] === undefined) {
            throw new Error("Required parameter \"params['status']\" missing.");
        }
        let data = {};
        data["Status"] = params["status"];
        if (params["routing"] !== undefined)
            data["Routing"] = serialize.object(params["routing"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelInstance(operationVersion, payload, instance._solution.interactionSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelContextImpl = InteractionChannelContextImpl;
class InteractionChannelInstance {
    constructor(_version, payload, interactionSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.interactionSid = payload.interaction_sid;
        this.type = payload.type;
        this.status = payload.status;
        this.errorCode = deserialize.integer(payload.error_code);
        this.errorMessage = payload.error_message;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { interactionSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new InteractionChannelContextImpl(this._version, this._solution.interactionSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a InteractionChannelInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed InteractionChannelInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the invites.
     */
    invites() {
        return this._proxy.invites;
    }
    /**
     * Access the participants.
     */
    participants() {
        return this._proxy.participants;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            interactionSid: this.interactionSid,
            type: this.type,
            status: this.status,
            errorCode: this.errorCode,
            errorMessage: this.errorMessage,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelInstance = InteractionChannelInstance;
function InteractionChannelListInstance(version, interactionSid) {
    if (!(0, utility_1.isValidPathParam)(interactionSid)) {
        throw new Error("Parameter 'interactionSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new InteractionChannelContextImpl(version, interactionSid, sid);
    };
    instance._version = version;
    instance._solution = { interactionSid };
    instance._uri = `/Interactions/${interactionSid}/Channels`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new InteractionChannelPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InteractionChannelListInstance = InteractionChannelListInstance;
class InteractionChannelPage extends Page_1.default {
    /**
     * Initialize the InteractionChannelPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of InteractionChannelInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new InteractionChannelInstance(this._version, payload, this._solution.interactionSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelPage = InteractionChannelPage;
