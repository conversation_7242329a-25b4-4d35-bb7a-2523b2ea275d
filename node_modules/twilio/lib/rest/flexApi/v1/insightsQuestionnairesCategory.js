"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsightsQuestionnairesCategoryPage = exports.InsightsQuestionnairesCategoryListInstance = exports.InsightsQuestionnairesCategoryInstance = exports.InsightsQuestionnairesCategoryContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class InsightsQuestionnairesCategoryContextImpl {
    constructor(_version, categorySid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(categorySid)) {
            throw new Error("Parameter 'categorySid' is not valid.");
        }
        this._solution = { categorySid };
        this._uri = `/Insights/QualityManagement/Categories/${categorySid}`;
    }
    remove(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            params: data,
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["name"] === null || params["name"] === undefined) {
            throw new Error("Required parameter \"params['name']\" missing.");
        }
        let data = {};
        data["Name"] = params["name"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesCategoryInstance(operationVersion, payload, instance._solution.categorySid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesCategoryContextImpl = InsightsQuestionnairesCategoryContextImpl;
class InsightsQuestionnairesCategoryInstance {
    constructor(_version, payload, categorySid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.categorySid = payload.category_sid;
        this.name = payload.name;
        this.url = payload.url;
        this._solution = { categorySid: categorySid || this.categorySid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new InsightsQuestionnairesCategoryContextImpl(this._version, this._solution.categorySid);
        return this._context;
    }
    remove(params, callback) {
        return this._proxy.remove(params, callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            categorySid: this.categorySid,
            name: this.name,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesCategoryInstance = InsightsQuestionnairesCategoryInstance;
function InsightsQuestionnairesCategoryListInstance(version) {
    const instance = ((categorySid) => instance.get(categorySid));
    instance.get = function get(categorySid) {
        return new InsightsQuestionnairesCategoryContextImpl(version, categorySid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Insights/QualityManagement/Categories`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["name"] === null || params["name"] === undefined) {
            throw new Error("Required parameter \"params['name']\" missing.");
        }
        let data = {};
        data["Name"] = params["name"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesCategoryInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesCategoryPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new InsightsQuestionnairesCategoryPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InsightsQuestionnairesCategoryListInstance = InsightsQuestionnairesCategoryListInstance;
class InsightsQuestionnairesCategoryPage extends Page_1.default {
    /**
     * Initialize the InsightsQuestionnairesCategoryPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of InsightsQuestionnairesCategoryInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new InsightsQuestionnairesCategoryInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesCategoryPage = InsightsQuestionnairesCategoryPage;
