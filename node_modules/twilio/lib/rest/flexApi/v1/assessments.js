"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssessmentsPage = exports.AssessmentsListInstance = exports.AssessmentsInstance = exports.AssessmentsContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class AssessmentsContextImpl {
    constructor(_version, assessmentSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(assessmentSid)) {
            throw new Error("Parameter 'assessmentSid' is not valid.");
        }
        this._solution = { assessmentSid };
        this._uri = `/Insights/QualityManagement/Assessments/${assessmentSid}`;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["offset"] === null || params["offset"] === undefined) {
            throw new Error("Required parameter \"params['offset']\" missing.");
        }
        if (params["answerText"] === null || params["answerText"] === undefined) {
            throw new Error("Required parameter \"params['answerText']\" missing.");
        }
        if (params["answerId"] === null || params["answerId"] === undefined) {
            throw new Error("Required parameter \"params['answerId']\" missing.");
        }
        let data = {};
        data["Offset"] = params["offset"];
        data["AnswerText"] = params["answerText"];
        data["AnswerId"] = params["answerId"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssessmentsInstance(operationVersion, payload, instance._solution.assessmentSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssessmentsContextImpl = AssessmentsContextImpl;
class AssessmentsInstance {
    constructor(_version, payload, assessmentSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.assessmentSid = payload.assessment_sid;
        this.offset = payload.offset;
        this.report = payload.report;
        this.weight = payload.weight;
        this.agentId = payload.agent_id;
        this.segmentId = payload.segment_id;
        this.userName = payload.user_name;
        this.userEmail = payload.user_email;
        this.answerText = payload.answer_text;
        this.answerId = payload.answer_id;
        this.assessment = payload.assessment;
        this.timestamp = payload.timestamp;
        this.url = payload.url;
        this._solution = { assessmentSid: assessmentSid || this.assessmentSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AssessmentsContextImpl(this._version, this._solution.assessmentSid);
        return this._context;
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            assessmentSid: this.assessmentSid,
            offset: this.offset,
            report: this.report,
            weight: this.weight,
            agentId: this.agentId,
            segmentId: this.segmentId,
            userName: this.userName,
            userEmail: this.userEmail,
            answerText: this.answerText,
            answerId: this.answerId,
            assessment: this.assessment,
            timestamp: this.timestamp,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssessmentsInstance = AssessmentsInstance;
function AssessmentsListInstance(version) {
    const instance = ((assessmentSid) => instance.get(assessmentSid));
    instance.get = function get(assessmentSid) {
        return new AssessmentsContextImpl(version, assessmentSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Insights/QualityManagement/Assessments`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["categorySid"] === null || params["categorySid"] === undefined) {
            throw new Error("Required parameter \"params['categorySid']\" missing.");
        }
        if (params["categoryName"] === null ||
            params["categoryName"] === undefined) {
            throw new Error("Required parameter \"params['categoryName']\" missing.");
        }
        if (params["segmentId"] === null || params["segmentId"] === undefined) {
            throw new Error("Required parameter \"params['segmentId']\" missing.");
        }
        if (params["agentId"] === null || params["agentId"] === undefined) {
            throw new Error("Required parameter \"params['agentId']\" missing.");
        }
        if (params["offset"] === null || params["offset"] === undefined) {
            throw new Error("Required parameter \"params['offset']\" missing.");
        }
        if (params["metricId"] === null || params["metricId"] === undefined) {
            throw new Error("Required parameter \"params['metricId']\" missing.");
        }
        if (params["metricName"] === null || params["metricName"] === undefined) {
            throw new Error("Required parameter \"params['metricName']\" missing.");
        }
        if (params["answerText"] === null || params["answerText"] === undefined) {
            throw new Error("Required parameter \"params['answerText']\" missing.");
        }
        if (params["answerId"] === null || params["answerId"] === undefined) {
            throw new Error("Required parameter \"params['answerId']\" missing.");
        }
        if (params["questionnaireSid"] === null ||
            params["questionnaireSid"] === undefined) {
            throw new Error("Required parameter \"params['questionnaireSid']\" missing.");
        }
        let data = {};
        data["CategorySid"] = params["categorySid"];
        data["CategoryName"] = params["categoryName"];
        data["SegmentId"] = params["segmentId"];
        data["AgentId"] = params["agentId"];
        data["Offset"] = params["offset"];
        data["MetricId"] = params["metricId"];
        data["MetricName"] = params["metricName"];
        data["AnswerText"] = params["answerText"];
        data["AnswerId"] = params["answerId"];
        data["QuestionnaireSid"] = params["questionnaireSid"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssessmentsInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["segmentId"] !== undefined)
            data["SegmentId"] = params["segmentId"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AssessmentsPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AssessmentsPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AssessmentsListInstance = AssessmentsListInstance;
class AssessmentsPage extends Page_1.default {
    /**
     * Initialize the AssessmentsPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AssessmentsInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AssessmentsInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AssessmentsPage = AssessmentsPage;
