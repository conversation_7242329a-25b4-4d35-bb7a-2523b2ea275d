"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageListInstance = exports.UsageInstance = exports.UsageContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class UsageContextImpl {
    constructor(_version, simSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(simSid)) {
            throw new Error("Parameter 'simSid' is not valid.");
        }
        this._solution = { simSid };
        this._uri = `/Sims/${simSid}/Usage`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["end"] !== undefined)
            data["End"] = params["end"];
        if (params["start"] !== undefined)
            data["Start"] = params["start"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UsageInstance(operationVersion, payload, instance._solution.simSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsageContextImpl = UsageContextImpl;
class UsageInstance {
    constructor(_version, payload, simSid) {
        this._version = _version;
        this.simSid = payload.sim_sid;
        this.simUniqueName = payload.sim_unique_name;
        this.accountSid = payload.account_sid;
        this.period = payload.period;
        this.commandsUsage = payload.commands_usage;
        this.commandsCosts = payload.commands_costs;
        this.dataUsage = payload.data_usage;
        this.dataCosts = payload.data_costs;
        this.url = payload.url;
        this._solution = { simSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new UsageContextImpl(this._version, this._solution.simSid);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            simSid: this.simSid,
            simUniqueName: this.simUniqueName,
            accountSid: this.accountSid,
            period: this.period,
            commandsUsage: this.commandsUsage,
            commandsCosts: this.commandsCosts,
            dataUsage: this.dataUsage,
            dataCosts: this.dataCosts,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsageInstance = UsageInstance;
function UsageListInstance(version, simSid) {
    if (!(0, utility_1.isValidPathParam)(simSid)) {
        throw new Error("Parameter 'simSid' is not valid.");
    }
    const instance = (() => instance.get());
    instance.get = function get() {
        return new UsageContextImpl(version, simSid);
    };
    instance._version = version;
    instance._solution = { simSid };
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UsageListInstance = UsageListInstance;
