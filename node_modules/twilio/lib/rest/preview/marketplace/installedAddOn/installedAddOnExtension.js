"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstalledAddOnExtensionPage = exports.InstalledAddOnExtensionListInstance = exports.InstalledAddOnExtensionInstance = exports.InstalledAddOnExtensionContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class InstalledAddOnExtensionContextImpl {
    constructor(_version, installedAddOnSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(installedAddOnSid)) {
            throw new Error("Parameter 'installedAddOnSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { installedAddOnSid, sid };
        this._uri = `/InstalledAddOns/${installedAddOnSid}/Extensions/${sid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new InstalledAddOnExtensionInstance(operationVersion, payload, instance._solution.installedAddOnSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["enabled"] === null || params["enabled"] === undefined) {
            throw new Error("Required parameter \"params['enabled']\" missing.");
        }
        let data = {};
        data["Enabled"] = serialize.bool(params["enabled"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InstalledAddOnExtensionInstance(operationVersion, payload, instance._solution.installedAddOnSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InstalledAddOnExtensionContextImpl = InstalledAddOnExtensionContextImpl;
class InstalledAddOnExtensionInstance {
    constructor(_version, payload, installedAddOnSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.installedAddOnSid = payload.installed_add_on_sid;
        this.friendlyName = payload.friendly_name;
        this.productName = payload.product_name;
        this.uniqueName = payload.unique_name;
        this.enabled = payload.enabled;
        this.url = payload.url;
        this._solution = { installedAddOnSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new InstalledAddOnExtensionContextImpl(this._version, this._solution.installedAddOnSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a InstalledAddOnExtensionInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed InstalledAddOnExtensionInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            installedAddOnSid: this.installedAddOnSid,
            friendlyName: this.friendlyName,
            productName: this.productName,
            uniqueName: this.uniqueName,
            enabled: this.enabled,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InstalledAddOnExtensionInstance = InstalledAddOnExtensionInstance;
function InstalledAddOnExtensionListInstance(version, installedAddOnSid) {
    if (!(0, utility_1.isValidPathParam)(installedAddOnSid)) {
        throw new Error("Parameter 'installedAddOnSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new InstalledAddOnExtensionContextImpl(version, installedAddOnSid, sid);
    };
    instance._version = version;
    instance._solution = { installedAddOnSid };
    instance._uri = `/InstalledAddOns/${installedAddOnSid}/Extensions`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InstalledAddOnExtensionPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new InstalledAddOnExtensionPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InstalledAddOnExtensionListInstance = InstalledAddOnExtensionListInstance;
class InstalledAddOnExtensionPage extends Page_1.default {
    /**
     * Initialize the InstalledAddOnExtensionPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of InstalledAddOnExtensionInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new InstalledAddOnExtensionInstance(this._version, payload, this._solution.installedAddOnSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InstalledAddOnExtensionPage = InstalledAddOnExtensionPage;
