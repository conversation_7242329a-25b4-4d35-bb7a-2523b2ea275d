"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const authorizationDocument_1 = require("./hosted_numbers/authorizationDocument");
const hostedNumberOrder_1 = require("./hosted_numbers/hostedNumberOrder");
class HostedNumbers extends Version_1.default {
    /**
     * Initialize the HostedNumbers version of Preview
     *
     * @param domain - The Twilio (Twilio.Preview) domain
     */
    constructor(domain) {
        super(domain, "HostedNumbers");
    }
    /** Getter for authorizationDocuments resource */
    get authorizationDocuments() {
        this._authorizationDocuments =
            this._authorizationDocuments || (0, authorizationDocument_1.AuthorizationDocumentListInstance)(this);
        return this._authorizationDocuments;
    }
    /** Getter for hostedNumberOrders resource */
    get hostedNumberOrders() {
        this._hostedNumberOrders =
            this._hostedNumberOrders || (0, hostedNumberOrder_1.HostedNumberOrderListInstance)(this);
        return this._hostedNumberOrders;
    }
}
exports.default = HostedNumbers;
