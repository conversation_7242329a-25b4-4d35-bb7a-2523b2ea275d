"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevicePage = exports.DeviceListInstance = exports.DeviceInstance = exports.DeviceContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class DeviceContextImpl {
    constructor(_version, fleetSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(fleetSid)) {
            throw new Error("Parameter 'fleetSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { fleetSid, sid };
        this._uri = `/Fleets/${fleetSid}/Devices/${sid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new DeviceInstance(operationVersion, payload, instance._solution.fleetSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["identity"] !== undefined)
            data["Identity"] = params["identity"];
        if (params["deploymentSid"] !== undefined)
            data["DeploymentSid"] = params["deploymentSid"];
        if (params["enabled"] !== undefined)
            data["Enabled"] = serialize.bool(params["enabled"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new DeviceInstance(operationVersion, payload, instance._solution.fleetSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DeviceContextImpl = DeviceContextImpl;
class DeviceInstance {
    constructor(_version, payload, fleetSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.url = payload.url;
        this.uniqueName = payload.unique_name;
        this.friendlyName = payload.friendly_name;
        this.fleetSid = payload.fleet_sid;
        this.enabled = payload.enabled;
        this.accountSid = payload.account_sid;
        this.identity = payload.identity;
        this.deploymentSid = payload.deployment_sid;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.dateAuthenticated = deserialize.iso8601DateTime(payload.date_authenticated);
        this._solution = { fleetSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new DeviceContextImpl(this._version, this._solution.fleetSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a DeviceInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a DeviceInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed DeviceInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            url: this.url,
            uniqueName: this.uniqueName,
            friendlyName: this.friendlyName,
            fleetSid: this.fleetSid,
            enabled: this.enabled,
            accountSid: this.accountSid,
            identity: this.identity,
            deploymentSid: this.deploymentSid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            dateAuthenticated: this.dateAuthenticated,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DeviceInstance = DeviceInstance;
function DeviceListInstance(version, fleetSid) {
    if (!(0, utility_1.isValidPathParam)(fleetSid)) {
        throw new Error("Parameter 'fleetSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new DeviceContextImpl(version, fleetSid, sid);
    };
    instance._version = version;
    instance._solution = { fleetSid };
    instance._uri = `/Fleets/${fleetSid}/Devices`;
    instance.create = function create(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["uniqueName"] !== undefined)
            data["UniqueName"] = params["uniqueName"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["identity"] !== undefined)
            data["Identity"] = params["identity"];
        if (params["deploymentSid"] !== undefined)
            data["DeploymentSid"] = params["deploymentSid"];
        if (params["enabled"] !== undefined)
            data["Enabled"] = serialize.bool(params["enabled"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new DeviceInstance(operationVersion, payload, instance._solution.fleetSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["deploymentSid"] !== undefined)
            data["DeploymentSid"] = params["deploymentSid"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new DevicePage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new DevicePage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.DeviceListInstance = DeviceListInstance;
class DevicePage extends Page_1.default {
    /**
     * Initialize the DevicePage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of DeviceInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new DeviceInstance(this._version, payload, this._solution.fleetSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DevicePage = DevicePage;
