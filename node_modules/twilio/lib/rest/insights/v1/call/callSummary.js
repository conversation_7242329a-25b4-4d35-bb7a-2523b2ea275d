"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallSummaryListInstance = exports.CallSummaryInstance = exports.CallSummaryContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class CallSummaryContextImpl {
    constructor(_version, callSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(callSid)) {
            throw new Error("Parameter 'callSid' is not valid.");
        }
        this._solution = { callSid };
        this._uri = `/Voice/${callSid}/Summary`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["processingState"] !== undefined)
            data["ProcessingState"] = params["processingState"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CallSummaryInstance(operationVersion, payload, instance._solution.callSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CallSummaryContextImpl = CallSummaryContextImpl;
class CallSummaryInstance {
    constructor(_version, payload, callSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.callSid = payload.call_sid;
        this.callType = payload.call_type;
        this.callState = payload.call_state;
        this.answeredBy = payload.answered_by;
        this.processingState = payload.processing_state;
        this.createdTime = deserialize.iso8601DateTime(payload.created_time);
        this.startTime = deserialize.iso8601DateTime(payload.start_time);
        this.endTime = deserialize.iso8601DateTime(payload.end_time);
        this.duration = deserialize.integer(payload.duration);
        this.connectDuration = deserialize.integer(payload.connect_duration);
        this.from = payload.from;
        this.to = payload.to;
        this.carrierEdge = payload.carrier_edge;
        this.clientEdge = payload.client_edge;
        this.sdkEdge = payload.sdk_edge;
        this.sipEdge = payload.sip_edge;
        this.tags = payload.tags;
        this.url = payload.url;
        this.attributes = payload.attributes;
        this.properties = payload.properties;
        this.trust = payload.trust;
        this.annotation = payload.annotation;
        this._solution = { callSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new CallSummaryContextImpl(this._version, this._solution.callSid);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            callSid: this.callSid,
            callType: this.callType,
            callState: this.callState,
            answeredBy: this.answeredBy,
            processingState: this.processingState,
            createdTime: this.createdTime,
            startTime: this.startTime,
            endTime: this.endTime,
            duration: this.duration,
            connectDuration: this.connectDuration,
            from: this.from,
            to: this.to,
            carrierEdge: this.carrierEdge,
            clientEdge: this.clientEdge,
            sdkEdge: this.sdkEdge,
            sipEdge: this.sipEdge,
            tags: this.tags,
            url: this.url,
            attributes: this.attributes,
            properties: this.properties,
            trust: this.trust,
            annotation: this.annotation,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CallSummaryInstance = CallSummaryInstance;
function CallSummaryListInstance(version, callSid) {
    if (!(0, utility_1.isValidPathParam)(callSid)) {
        throw new Error("Parameter 'callSid' is not valid.");
    }
    const instance = (() => instance.get());
    instance.get = function get() {
        return new CallSummaryContextImpl(version, callSid);
    };
    instance._version = version;
    instance._solution = { callSid };
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.CallSummaryListInstance = CallSummaryListInstance;
