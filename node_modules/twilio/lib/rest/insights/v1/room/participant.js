"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParticipantPage = exports.ParticipantListInstance = exports.ParticipantInstance = exports.ParticipantContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class ParticipantContextImpl {
    constructor(_version, roomSid, participantSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(roomSid)) {
            throw new Error("Parameter 'roomSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(participantSid)) {
            throw new Error("Parameter 'participantSid' is not valid.");
        }
        this._solution = { roomSid, participantSid };
        this._uri = `/Video/Rooms/${roomSid}/Participants/${participantSid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new ParticipantInstance(operationVersion, payload, instance._solution.roomSid, instance._solution.participantSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantContextImpl = ParticipantContextImpl;
class ParticipantInstance {
    constructor(_version, payload, roomSid, participantSid) {
        this._version = _version;
        this.participantSid = payload.participant_sid;
        this.participantIdentity = payload.participant_identity;
        this.joinTime = deserialize.iso8601DateTime(payload.join_time);
        this.leaveTime = deserialize.iso8601DateTime(payload.leave_time);
        this.durationSec = payload.duration_sec;
        this.accountSid = payload.account_sid;
        this.roomSid = payload.room_sid;
        this.status = payload.status;
        this.codecs = payload.codecs;
        this.endReason = payload.end_reason;
        this.errorCode = deserialize.integer(payload.error_code);
        this.errorCodeUrl = payload.error_code_url;
        this.mediaRegion = payload.media_region;
        this.properties = payload.properties;
        this.edgeLocation = payload.edge_location;
        this.publisherInfo = payload.publisher_info;
        this.url = payload.url;
        this._solution = {
            roomSid,
            participantSid: participantSid || this.participantSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ParticipantContextImpl(this._version, this._solution.roomSid, this._solution.participantSid);
        return this._context;
    }
    /**
     * Fetch a ParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ParticipantInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            participantSid: this.participantSid,
            participantIdentity: this.participantIdentity,
            joinTime: this.joinTime,
            leaveTime: this.leaveTime,
            durationSec: this.durationSec,
            accountSid: this.accountSid,
            roomSid: this.roomSid,
            status: this.status,
            codecs: this.codecs,
            endReason: this.endReason,
            errorCode: this.errorCode,
            errorCodeUrl: this.errorCodeUrl,
            mediaRegion: this.mediaRegion,
            properties: this.properties,
            edgeLocation: this.edgeLocation,
            publisherInfo: this.publisherInfo,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantInstance = ParticipantInstance;
function ParticipantListInstance(version, roomSid) {
    if (!(0, utility_1.isValidPathParam)(roomSid)) {
        throw new Error("Parameter 'roomSid' is not valid.");
    }
    const instance = ((participantSid) => instance.get(participantSid));
    instance.get = function get(participantSid) {
        return new ParticipantContextImpl(version, roomSid, participantSid);
    };
    instance._version = version;
    instance._solution = { roomSid };
    instance._uri = `/Video/Rooms/${roomSid}/Participants`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ParticipantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ParticipantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ParticipantListInstance = ParticipantListInstance;
class ParticipantPage extends Page_1.default {
    /**
     * Initialize the ParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ParticipantInstance(this._version, payload, this._solution.roomSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ParticipantPage = ParticipantPage;
