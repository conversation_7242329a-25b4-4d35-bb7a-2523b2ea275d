"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoomPage = exports.RoomListInstance = exports.RoomInstance = exports.RoomContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const participant_1 = require("./room/participant");
class RoomContextImpl {
    constructor(_version, roomSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(roomSid)) {
            throw new Error("Parameter 'roomSid' is not valid.");
        }
        this._solution = { roomSid };
        this._uri = `/Video/Rooms/${roomSid}`;
    }
    get participants() {
        this._participants =
            this._participants ||
                (0, participant_1.ParticipantListInstance)(this._version, this._solution.roomSid);
        return this._participants;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new RoomInstance(operationVersion, payload, instance._solution.roomSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoomContextImpl = RoomContextImpl;
class RoomInstance {
    constructor(_version, payload, roomSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.roomSid = payload.room_sid;
        this.roomName = payload.room_name;
        this.createTime = deserialize.iso8601DateTime(payload.create_time);
        this.endTime = deserialize.iso8601DateTime(payload.end_time);
        this.roomType = payload.room_type;
        this.roomStatus = payload.room_status;
        this.statusCallback = payload.status_callback;
        this.statusCallbackMethod = payload.status_callback_method;
        this.createdMethod = payload.created_method;
        this.endReason = payload.end_reason;
        this.maxParticipants = deserialize.integer(payload.max_participants);
        this.uniqueParticipants = deserialize.integer(payload.unique_participants);
        this.uniqueParticipantIdentities = deserialize.integer(payload.unique_participant_identities);
        this.concurrentParticipants = deserialize.integer(payload.concurrent_participants);
        this.maxConcurrentParticipants = deserialize.integer(payload.max_concurrent_participants);
        this.codecs = payload.codecs;
        this.mediaRegion = payload.media_region;
        this.durationSec = payload.duration_sec;
        this.totalParticipantDurationSec = payload.total_participant_duration_sec;
        this.totalRecordingDurationSec = payload.total_recording_duration_sec;
        this.processingState = payload.processing_state;
        this.recordingEnabled = payload.recording_enabled;
        this.edgeLocation = payload.edge_location;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { roomSid: roomSid || this.roomSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new RoomContextImpl(this._version, this._solution.roomSid);
        return this._context;
    }
    /**
     * Fetch a RoomInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed RoomInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the participants.
     */
    participants() {
        return this._proxy.participants;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            roomSid: this.roomSid,
            roomName: this.roomName,
            createTime: this.createTime,
            endTime: this.endTime,
            roomType: this.roomType,
            roomStatus: this.roomStatus,
            statusCallback: this.statusCallback,
            statusCallbackMethod: this.statusCallbackMethod,
            createdMethod: this.createdMethod,
            endReason: this.endReason,
            maxParticipants: this.maxParticipants,
            uniqueParticipants: this.uniqueParticipants,
            uniqueParticipantIdentities: this.uniqueParticipantIdentities,
            concurrentParticipants: this.concurrentParticipants,
            maxConcurrentParticipants: this.maxConcurrentParticipants,
            codecs: this.codecs,
            mediaRegion: this.mediaRegion,
            durationSec: this.durationSec,
            totalParticipantDurationSec: this.totalParticipantDurationSec,
            totalRecordingDurationSec: this.totalRecordingDurationSec,
            processingState: this.processingState,
            recordingEnabled: this.recordingEnabled,
            edgeLocation: this.edgeLocation,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoomInstance = RoomInstance;
function RoomListInstance(version) {
    const instance = ((roomSid) => instance.get(roomSid));
    instance.get = function get(roomSid) {
        return new RoomContextImpl(version, roomSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Video/Rooms`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["roomType"] !== undefined)
            data["RoomType"] = serialize.map(params["roomType"], (e) => e);
        if (params["codec"] !== undefined)
            data["Codec"] = serialize.map(params["codec"], (e) => e);
        if (params["roomName"] !== undefined)
            data["RoomName"] = params["roomName"];
        if (params["createdAfter"] !== undefined)
            data["CreatedAfter"] = serialize.iso8601DateTime(params["createdAfter"]);
        if (params["createdBefore"] !== undefined)
            data["CreatedBefore"] = serialize.iso8601DateTime(params["createdBefore"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new RoomPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new RoomPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.RoomListInstance = RoomListInstance;
class RoomPage extends Page_1.default {
    /**
     * Initialize the RoomPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of RoomInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new RoomInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoomPage = RoomPage;
