"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConferenceParticipantPage = exports.ConferenceParticipantListInstance = exports.ConferenceParticipantInstance = exports.ConferenceParticipantContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class ConferenceParticipantContextImpl {
    constructor(_version, conferenceSid, participantSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(conferenceSid)) {
            throw new Error("Parameter 'conferenceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(participantSid)) {
            throw new Error("Parameter 'participantSid' is not valid.");
        }
        this._solution = { conferenceSid, participantSid };
        this._uri = `/Conferences/${conferenceSid}/Participants/${participantSid}`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["events"] !== undefined)
            data["Events"] = params["events"];
        if (params["metrics"] !== undefined)
            data["Metrics"] = params["metrics"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ConferenceParticipantInstance(operationVersion, payload, instance._solution.conferenceSid, instance._solution.participantSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConferenceParticipantContextImpl = ConferenceParticipantContextImpl;
class ConferenceParticipantInstance {
    constructor(_version, payload, conferenceSid, participantSid) {
        this._version = _version;
        this.participantSid = payload.participant_sid;
        this.label = payload.label;
        this.conferenceSid = payload.conference_sid;
        this.callSid = payload.call_sid;
        this.accountSid = payload.account_sid;
        this.callDirection = payload.call_direction;
        this.from = payload.from;
        this.to = payload.to;
        this.callStatus = payload.call_status;
        this.countryCode = payload.country_code;
        this.isModerator = payload.is_moderator;
        this.joinTime = deserialize.iso8601DateTime(payload.join_time);
        this.leaveTime = deserialize.iso8601DateTime(payload.leave_time);
        this.durationSeconds = deserialize.integer(payload.duration_seconds);
        this.outboundQueueLength = deserialize.integer(payload.outbound_queue_length);
        this.outboundTimeInQueue = deserialize.integer(payload.outbound_time_in_queue);
        this.jitterBufferSize = payload.jitter_buffer_size;
        this.isCoach = payload.is_coach;
        this.coachedParticipants = payload.coached_participants;
        this.participantRegion = payload.participant_region;
        this.conferenceRegion = payload.conference_region;
        this.callType = payload.call_type;
        this.processingState = payload.processing_state;
        this.properties = payload.properties;
        this.events = payload.events;
        this.metrics = payload.metrics;
        this.url = payload.url;
        this._solution = {
            conferenceSid,
            participantSid: participantSid || this.participantSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ConferenceParticipantContextImpl(this._version, this._solution.conferenceSid, this._solution.participantSid);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            participantSid: this.participantSid,
            label: this.label,
            conferenceSid: this.conferenceSid,
            callSid: this.callSid,
            accountSid: this.accountSid,
            callDirection: this.callDirection,
            from: this.from,
            to: this.to,
            callStatus: this.callStatus,
            countryCode: this.countryCode,
            isModerator: this.isModerator,
            joinTime: this.joinTime,
            leaveTime: this.leaveTime,
            durationSeconds: this.durationSeconds,
            outboundQueueLength: this.outboundQueueLength,
            outboundTimeInQueue: this.outboundTimeInQueue,
            jitterBufferSize: this.jitterBufferSize,
            isCoach: this.isCoach,
            coachedParticipants: this.coachedParticipants,
            participantRegion: this.participantRegion,
            conferenceRegion: this.conferenceRegion,
            callType: this.callType,
            processingState: this.processingState,
            properties: this.properties,
            events: this.events,
            metrics: this.metrics,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConferenceParticipantInstance = ConferenceParticipantInstance;
function ConferenceParticipantListInstance(version, conferenceSid) {
    if (!(0, utility_1.isValidPathParam)(conferenceSid)) {
        throw new Error("Parameter 'conferenceSid' is not valid.");
    }
    const instance = ((participantSid) => instance.get(participantSid));
    instance.get = function get(participantSid) {
        return new ConferenceParticipantContextImpl(version, conferenceSid, participantSid);
    };
    instance._version = version;
    instance._solution = { conferenceSid };
    instance._uri = `/Conferences/${conferenceSid}/Participants`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["participantSid"] !== undefined)
            data["ParticipantSid"] = params["participantSid"];
        if (params["label"] !== undefined)
            data["Label"] = params["label"];
        if (params["events"] !== undefined)
            data["Events"] = params["events"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ConferenceParticipantPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ConferenceParticipantPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ConferenceParticipantListInstance = ConferenceParticipantListInstance;
class ConferenceParticipantPage extends Page_1.default {
    /**
     * Initialize the ConferenceParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ConferenceParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ConferenceParticipantInstance(this._version, payload, this._solution.conferenceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConferenceParticipantPage = ConferenceParticipantPage;
