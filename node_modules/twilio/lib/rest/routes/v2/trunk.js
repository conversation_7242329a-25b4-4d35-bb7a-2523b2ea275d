"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Routes
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrunkListInstance = exports.TrunkInstance = exports.TrunkContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class TrunkContextImpl {
    constructor(_version, sipTrunkDomain) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sipTrunkDomain)) {
            throw new Error("Parameter 'sipTrunkDomain' is not valid.");
        }
        this._solution = { sipTrunkDomain };
        this._uri = `/Trunks/${sipTrunkDomain}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new TrunkInstance(operationVersion, payload, instance._solution.sipTrunkDomain));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["voiceRegion"] !== undefined)
            data["VoiceRegion"] = params["voiceRegion"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TrunkInstance(operationVersion, payload, instance._solution.sipTrunkDomain));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TrunkContextImpl = TrunkContextImpl;
class TrunkInstance {
    constructor(_version, payload, sipTrunkDomain) {
        this._version = _version;
        this.sipTrunkDomain = payload.sip_trunk_domain;
        this.url = payload.url;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.friendlyName = payload.friendly_name;
        this.voiceRegion = payload.voice_region;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this._solution = { sipTrunkDomain: sipTrunkDomain || this.sipTrunkDomain };
    }
    get _proxy() {
        this._context =
            this._context ||
                new TrunkContextImpl(this._version, this._solution.sipTrunkDomain);
        return this._context;
    }
    /**
     * Fetch a TrunkInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed TrunkInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sipTrunkDomain: this.sipTrunkDomain,
            url: this.url,
            sid: this.sid,
            accountSid: this.accountSid,
            friendlyName: this.friendlyName,
            voiceRegion: this.voiceRegion,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TrunkInstance = TrunkInstance;
function TrunkListInstance(version) {
    const instance = ((sipTrunkDomain) => instance.get(sipTrunkDomain));
    instance.get = function get(sipTrunkDomain) {
        return new TrunkContextImpl(version, sipTrunkDomain);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.TrunkListInstance = TrunkListInstance;
