"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Pricing
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NumberListInstance = exports.NumberInstance = exports.NumberContextImpl = exports.PricingV2TrunkingNumberOriginatingCallPrice = exports.PricingV2TrunkingCountryInstanceTerminatingPrefixPrices = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class PricingV2TrunkingCountryInstanceTerminatingPrefixPrices {
}
exports.PricingV2TrunkingCountryInstanceTerminatingPrefixPrices = PricingV2TrunkingCountryInstanceTerminatingPrefixPrices;
/**
 * The [OriginatingCallPrice](https://www.twilio.com/docs/voice/pricing#inbound-call-price) record.
 */
class PricingV2TrunkingNumberOriginatingCallPrice {
}
exports.PricingV2TrunkingNumberOriginatingCallPrice = PricingV2TrunkingNumberOriginatingCallPrice;
class NumberContextImpl {
    constructor(_version, destinationNumber) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(destinationNumber)) {
            throw new Error("Parameter 'destinationNumber' is not valid.");
        }
        this._solution = { destinationNumber };
        this._uri = `/Trunking/Numbers/${destinationNumber}`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["originationNumber"] !== undefined)
            data["OriginationNumber"] = params["originationNumber"];
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new NumberInstance(operationVersion, payload, instance._solution.destinationNumber));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.NumberContextImpl = NumberContextImpl;
class NumberInstance {
    constructor(_version, payload, destinationNumber) {
        this._version = _version;
        this.destinationNumber = payload.destination_number;
        this.originationNumber = payload.origination_number;
        this.country = payload.country;
        this.isoCountry = payload.iso_country;
        this.terminatingPrefixPrices = payload.terminating_prefix_prices;
        this.originatingCallPrice = payload.originating_call_price;
        this.priceUnit = payload.price_unit;
        this.url = payload.url;
        this._solution = {
            destinationNumber: destinationNumber || this.destinationNumber,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new NumberContextImpl(this._version, this._solution.destinationNumber);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            destinationNumber: this.destinationNumber,
            originationNumber: this.originationNumber,
            country: this.country,
            isoCountry: this.isoCountry,
            terminatingPrefixPrices: this.terminatingPrefixPrices,
            originatingCallPrice: this.originatingCallPrice,
            priceUnit: this.priceUnit,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.NumberInstance = NumberInstance;
function NumberListInstance(version) {
    const instance = ((destinationNumber) => instance.get(destinationNumber));
    instance.get = function get(destinationNumber) {
        return new NumberContextImpl(version, destinationNumber);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.NumberListInstance = NumberListInstance;
