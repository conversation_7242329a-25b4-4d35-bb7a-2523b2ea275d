"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Pricing
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const messaging_1 = require("./v1/messaging");
const phoneNumber_1 = require("./v1/phoneNumber");
const voice_1 = require("./v1/voice");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Pricing
     *
     * @param domain - The Twilio (Twilio.Pricing) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for messaging resource */
    get messaging() {
        this._messaging = this._messaging || (0, messaging_1.MessagingListInstance)(this);
        return this._messaging;
    }
    /** Getter for phoneNumbers resource */
    get phoneNumbers() {
        this._phoneNumbers = this._phoneNumbers || (0, phoneNumber_1.PhoneNumberListInstance)(this);
        return this._phoneNumbers;
    }
    /** Getter for voice resource */
    get voice() {
        this._voice = this._voice || (0, voice_1.VoiceListInstance)(this);
        return this._voice;
    }
}
exports.default = V1;
