"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountPage = exports.AccountListInstance = exports.AccountInstance = exports.AccountContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const address_1 = require("./account/address");
const application_1 = require("./account/application");
const authorizedConnectApp_1 = require("./account/authorizedConnectApp");
const availablePhoneNumberCountry_1 = require("./account/availablePhoneNumberCountry");
const balance_1 = require("./account/balance");
const call_1 = require("./account/call");
const conference_1 = require("./account/conference");
const connectApp_1 = require("./account/connectApp");
const incomingPhoneNumber_1 = require("./account/incomingPhoneNumber");
const key_1 = require("./account/key");
const message_1 = require("./account/message");
const newKey_1 = require("./account/newKey");
const newSigningKey_1 = require("./account/newSigningKey");
const notification_1 = require("./account/notification");
const outgoingCallerId_1 = require("./account/outgoingCallerId");
const queue_1 = require("./account/queue");
const recording_1 = require("./account/recording");
const shortCode_1 = require("./account/shortCode");
const signingKey_1 = require("./account/signingKey");
const sip_1 = require("./account/sip");
const token_1 = require("./account/token");
const transcription_1 = require("./account/transcription");
const usage_1 = require("./account/usage");
const validationRequest_1 = require("./account/validationRequest");
class AccountContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Accounts/${sid}.json`;
    }
    get addresses() {
        this._addresses =
            this._addresses || (0, address_1.AddressListInstance)(this._version, this._solution.sid);
        return this._addresses;
    }
    get applications() {
        this._applications =
            this._applications ||
                (0, application_1.ApplicationListInstance)(this._version, this._solution.sid);
        return this._applications;
    }
    get authorizedConnectApps() {
        this._authorizedConnectApps =
            this._authorizedConnectApps ||
                (0, authorizedConnectApp_1.AuthorizedConnectAppListInstance)(this._version, this._solution.sid);
        return this._authorizedConnectApps;
    }
    get availablePhoneNumbers() {
        this._availablePhoneNumbers =
            this._availablePhoneNumbers ||
                (0, availablePhoneNumberCountry_1.AvailablePhoneNumberCountryListInstance)(this._version, this._solution.sid);
        return this._availablePhoneNumbers;
    }
    get balance() {
        this._balance =
            this._balance || (0, balance_1.BalanceListInstance)(this._version, this._solution.sid);
        return this._balance;
    }
    get calls() {
        this._calls =
            this._calls || (0, call_1.CallListInstance)(this._version, this._solution.sid);
        return this._calls;
    }
    get conferences() {
        this._conferences =
            this._conferences ||
                (0, conference_1.ConferenceListInstance)(this._version, this._solution.sid);
        return this._conferences;
    }
    get connectApps() {
        this._connectApps =
            this._connectApps ||
                (0, connectApp_1.ConnectAppListInstance)(this._version, this._solution.sid);
        return this._connectApps;
    }
    get incomingPhoneNumbers() {
        this._incomingPhoneNumbers =
            this._incomingPhoneNumbers ||
                (0, incomingPhoneNumber_1.IncomingPhoneNumberListInstance)(this._version, this._solution.sid);
        return this._incomingPhoneNumbers;
    }
    get keys() {
        this._keys =
            this._keys || (0, key_1.KeyListInstance)(this._version, this._solution.sid);
        return this._keys;
    }
    get messages() {
        this._messages =
            this._messages || (0, message_1.MessageListInstance)(this._version, this._solution.sid);
        return this._messages;
    }
    get newKeys() {
        this._newKeys =
            this._newKeys || (0, newKey_1.NewKeyListInstance)(this._version, this._solution.sid);
        return this._newKeys;
    }
    get newSigningKeys() {
        this._newSigningKeys =
            this._newSigningKeys ||
                (0, newSigningKey_1.NewSigningKeyListInstance)(this._version, this._solution.sid);
        return this._newSigningKeys;
    }
    get notifications() {
        this._notifications =
            this._notifications ||
                (0, notification_1.NotificationListInstance)(this._version, this._solution.sid);
        return this._notifications;
    }
    get outgoingCallerIds() {
        this._outgoingCallerIds =
            this._outgoingCallerIds ||
                (0, outgoingCallerId_1.OutgoingCallerIdListInstance)(this._version, this._solution.sid);
        return this._outgoingCallerIds;
    }
    get queues() {
        this._queues =
            this._queues || (0, queue_1.QueueListInstance)(this._version, this._solution.sid);
        return this._queues;
    }
    get recordings() {
        this._recordings =
            this._recordings ||
                (0, recording_1.RecordingListInstance)(this._version, this._solution.sid);
        return this._recordings;
    }
    get shortCodes() {
        this._shortCodes =
            this._shortCodes ||
                (0, shortCode_1.ShortCodeListInstance)(this._version, this._solution.sid);
        return this._shortCodes;
    }
    get signingKeys() {
        this._signingKeys =
            this._signingKeys ||
                (0, signingKey_1.SigningKeyListInstance)(this._version, this._solution.sid);
        return this._signingKeys;
    }
    get sip() {
        this._sip = this._sip || (0, sip_1.SipListInstance)(this._version, this._solution.sid);
        return this._sip;
    }
    get tokens() {
        this._tokens =
            this._tokens || (0, token_1.TokenListInstance)(this._version, this._solution.sid);
        return this._tokens;
    }
    get transcriptions() {
        this._transcriptions =
            this._transcriptions ||
                (0, transcription_1.TranscriptionListInstance)(this._version, this._solution.sid);
        return this._transcriptions;
    }
    get usage() {
        this._usage =
            this._usage || (0, usage_1.UsageListInstance)(this._version, this._solution.sid);
        return this._usage;
    }
    get validationRequests() {
        this._validationRequests =
            this._validationRequests ||
                (0, validationRequest_1.ValidationRequestListInstance)(this._version, this._solution.sid);
        return this._validationRequests;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new AccountInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AccountInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AccountContextImpl = AccountContextImpl;
class AccountInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.authToken = payload.auth_token;
        this.dateCreated = deserialize.rfc2822DateTime(payload.date_created);
        this.dateUpdated = deserialize.rfc2822DateTime(payload.date_updated);
        this.friendlyName = payload.friendly_name;
        this.ownerAccountSid = payload.owner_account_sid;
        this.sid = payload.sid;
        this.status = payload.status;
        this.subresourceUris = payload.subresource_uris;
        this.type = payload.type;
        this.uri = payload.uri;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AccountContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a AccountInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AccountInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the addresses.
     */
    addresses() {
        return this._proxy.addresses;
    }
    /**
     * Access the applications.
     */
    applications() {
        return this._proxy.applications;
    }
    /**
     * Access the authorizedConnectApps.
     */
    authorizedConnectApps() {
        return this._proxy.authorizedConnectApps;
    }
    /**
     * Access the availablePhoneNumbers.
     */
    availablePhoneNumbers() {
        return this._proxy.availablePhoneNumbers;
    }
    /**
     * Access the balance.
     */
    balance() {
        return this._proxy.balance;
    }
    /**
     * Access the calls.
     */
    calls() {
        return this._proxy.calls;
    }
    /**
     * Access the conferences.
     */
    conferences() {
        return this._proxy.conferences;
    }
    /**
     * Access the connectApps.
     */
    connectApps() {
        return this._proxy.connectApps;
    }
    /**
     * Access the incomingPhoneNumbers.
     */
    incomingPhoneNumbers() {
        return this._proxy.incomingPhoneNumbers;
    }
    /**
     * Access the keys.
     */
    keys() {
        return this._proxy.keys;
    }
    /**
     * Access the messages.
     */
    messages() {
        return this._proxy.messages;
    }
    /**
     * Access the newKeys.
     */
    newKeys() {
        return this._proxy.newKeys;
    }
    /**
     * Access the newSigningKeys.
     */
    newSigningKeys() {
        return this._proxy.newSigningKeys;
    }
    /**
     * Access the notifications.
     */
    notifications() {
        return this._proxy.notifications;
    }
    /**
     * Access the outgoingCallerIds.
     */
    outgoingCallerIds() {
        return this._proxy.outgoingCallerIds;
    }
    /**
     * Access the queues.
     */
    queues() {
        return this._proxy.queues;
    }
    /**
     * Access the recordings.
     */
    recordings() {
        return this._proxy.recordings;
    }
    /**
     * Access the shortCodes.
     */
    shortCodes() {
        return this._proxy.shortCodes;
    }
    /**
     * Access the signingKeys.
     */
    signingKeys() {
        return this._proxy.signingKeys;
    }
    /**
     * Access the sip.
     */
    sip() {
        return this._proxy.sip;
    }
    /**
     * Access the tokens.
     */
    tokens() {
        return this._proxy.tokens;
    }
    /**
     * Access the transcriptions.
     */
    transcriptions() {
        return this._proxy.transcriptions;
    }
    /**
     * Access the usage.
     */
    usage() {
        return this._proxy.usage;
    }
    /**
     * Access the validationRequests.
     */
    validationRequests() {
        return this._proxy.validationRequests;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            authToken: this.authToken,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            friendlyName: this.friendlyName,
            ownerAccountSid: this.ownerAccountSid,
            sid: this.sid,
            status: this.status,
            subresourceUris: this.subresourceUris,
            type: this.type,
            uri: this.uri,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AccountInstance = AccountInstance;
function AccountListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new AccountContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Accounts.json`;
    instance.create = function create(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AccountInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AccountPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AccountPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AccountListInstance = AccountListInstance;
class AccountPage extends Page_1.default {
    /**
     * Initialize the AccountPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AccountInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AccountInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AccountPage = AccountPage;
