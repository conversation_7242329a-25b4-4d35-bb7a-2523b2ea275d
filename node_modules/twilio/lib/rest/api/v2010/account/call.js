"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallPage = exports.CallListInstance = exports.CallInstance = exports.CallContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const event_1 = require("./call/event");
const notification_1 = require("./call/notification");
const payment_1 = require("./call/payment");
const recording_1 = require("./call/recording");
const siprec_1 = require("./call/siprec");
const stream_1 = require("./call/stream");
const userDefinedMessage_1 = require("./call/userDefinedMessage");
const userDefinedMessageSubscription_1 = require("./call/userDefinedMessageSubscription");
class CallContextImpl {
    constructor(_version, accountSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { accountSid, sid };
        this._uri = `/Accounts/${accountSid}/Calls/${sid}.json`;
    }
    get events() {
        this._events =
            this._events ||
                (0, event_1.EventListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._events;
    }
    get notifications() {
        this._notifications =
            this._notifications ||
                (0, notification_1.NotificationListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._notifications;
    }
    get payments() {
        this._payments =
            this._payments ||
                (0, payment_1.PaymentListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._payments;
    }
    get recordings() {
        this._recordings =
            this._recordings ||
                (0, recording_1.RecordingListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._recordings;
    }
    get siprec() {
        this._siprec =
            this._siprec ||
                (0, siprec_1.SiprecListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._siprec;
    }
    get streams() {
        this._streams =
            this._streams ||
                (0, stream_1.StreamListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._streams;
    }
    get userDefinedMessages() {
        this._userDefinedMessages =
            this._userDefinedMessages ||
                (0, userDefinedMessage_1.UserDefinedMessageListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._userDefinedMessages;
    }
    get userDefinedMessageSubscriptions() {
        this._userDefinedMessageSubscriptions =
            this._userDefinedMessageSubscriptions ||
                (0, userDefinedMessageSubscription_1.UserDefinedMessageSubscriptionListInstance)(this._version, this._solution.accountSid, this._solution.sid);
        return this._userDefinedMessageSubscriptions;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new CallInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["url"] !== undefined)
            data["Url"] = params["url"];
        if (params["method"] !== undefined)
            data["Method"] = params["method"];
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["fallbackUrl"] !== undefined)
            data["FallbackUrl"] = params["fallbackUrl"];
        if (params["fallbackMethod"] !== undefined)
            data["FallbackMethod"] = params["fallbackMethod"];
        if (params["statusCallback"] !== undefined)
            data["StatusCallback"] = params["statusCallback"];
        if (params["statusCallbackMethod"] !== undefined)
            data["StatusCallbackMethod"] = params["statusCallbackMethod"];
        if (params["twiml"] !== undefined)
            data["Twiml"] = serialize.twiml(params["twiml"]);
        if (params["timeLimit"] !== undefined)
            data["TimeLimit"] = params["timeLimit"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CallInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CallContextImpl = CallContextImpl;
class CallInstance {
    constructor(_version, payload, accountSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.dateCreated = deserialize.rfc2822DateTime(payload.date_created);
        this.dateUpdated = deserialize.rfc2822DateTime(payload.date_updated);
        this.parentCallSid = payload.parent_call_sid;
        this.accountSid = payload.account_sid;
        this.to = payload.to;
        this.toFormatted = payload.to_formatted;
        this.from = payload.from;
        this.fromFormatted = payload.from_formatted;
        this.phoneNumberSid = payload.phone_number_sid;
        this.status = payload.status;
        this.startTime = deserialize.rfc2822DateTime(payload.start_time);
        this.endTime = deserialize.rfc2822DateTime(payload.end_time);
        this.duration = payload.duration;
        this.price = payload.price;
        this.priceUnit = payload.price_unit;
        this.direction = payload.direction;
        this.answeredBy = payload.answered_by;
        this.apiVersion = payload.api_version;
        this.forwardedFrom = payload.forwarded_from;
        this.groupSid = payload.group_sid;
        this.callerName = payload.caller_name;
        this.queueTime = payload.queue_time;
        this.trunkSid = payload.trunk_sid;
        this.uri = payload.uri;
        this.subresourceUris = payload.subresource_uris;
        this._solution = { accountSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new CallContextImpl(this._version, this._solution.accountSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a CallInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a CallInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed CallInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the events.
     */
    events() {
        return this._proxy.events;
    }
    /**
     * Access the notifications.
     */
    notifications() {
        return this._proxy.notifications;
    }
    /**
     * Access the payments.
     */
    payments() {
        return this._proxy.payments;
    }
    /**
     * Access the recordings.
     */
    recordings() {
        return this._proxy.recordings;
    }
    /**
     * Access the siprec.
     */
    siprec() {
        return this._proxy.siprec;
    }
    /**
     * Access the streams.
     */
    streams() {
        return this._proxy.streams;
    }
    /**
     * Access the userDefinedMessages.
     */
    userDefinedMessages() {
        return this._proxy.userDefinedMessages;
    }
    /**
     * Access the userDefinedMessageSubscriptions.
     */
    userDefinedMessageSubscriptions() {
        return this._proxy.userDefinedMessageSubscriptions;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            parentCallSid: this.parentCallSid,
            accountSid: this.accountSid,
            to: this.to,
            toFormatted: this.toFormatted,
            from: this.from,
            fromFormatted: this.fromFormatted,
            phoneNumberSid: this.phoneNumberSid,
            status: this.status,
            startTime: this.startTime,
            endTime: this.endTime,
            duration: this.duration,
            price: this.price,
            priceUnit: this.priceUnit,
            direction: this.direction,
            answeredBy: this.answeredBy,
            apiVersion: this.apiVersion,
            forwardedFrom: this.forwardedFrom,
            groupSid: this.groupSid,
            callerName: this.callerName,
            queueTime: this.queueTime,
            trunkSid: this.trunkSid,
            uri: this.uri,
            subresourceUris: this.subresourceUris,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CallInstance = CallInstance;
function CallListInstance(version, accountSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new CallContextImpl(version, accountSid, sid);
    };
    instance._version = version;
    instance._solution = { accountSid };
    instance._uri = `/Accounts/${accountSid}/Calls.json`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["to"] === null || params["to"] === undefined) {
            throw new Error("Required parameter \"params['to']\" missing.");
        }
        if (params["from"] === null || params["from"] === undefined) {
            throw new Error("Required parameter \"params['from']\" missing.");
        }
        let data = {};
        data["To"] = params["to"];
        data["From"] = params["from"];
        if (params["method"] !== undefined)
            data["Method"] = params["method"];
        if (params["fallbackUrl"] !== undefined)
            data["FallbackUrl"] = params["fallbackUrl"];
        if (params["fallbackMethod"] !== undefined)
            data["FallbackMethod"] = params["fallbackMethod"];
        if (params["statusCallback"] !== undefined)
            data["StatusCallback"] = params["statusCallback"];
        if (params["statusCallbackEvent"] !== undefined)
            data["StatusCallbackEvent"] = serialize.map(params["statusCallbackEvent"], (e) => e);
        if (params["statusCallbackMethod"] !== undefined)
            data["StatusCallbackMethod"] = params["statusCallbackMethod"];
        if (params["sendDigits"] !== undefined)
            data["SendDigits"] = params["sendDigits"];
        if (params["timeout"] !== undefined)
            data["Timeout"] = params["timeout"];
        if (params["record"] !== undefined)
            data["Record"] = serialize.bool(params["record"]);
        if (params["recordingChannels"] !== undefined)
            data["RecordingChannels"] = params["recordingChannels"];
        if (params["recordingStatusCallback"] !== undefined)
            data["RecordingStatusCallback"] = params["recordingStatusCallback"];
        if (params["recordingStatusCallbackMethod"] !== undefined)
            data["RecordingStatusCallbackMethod"] =
                params["recordingStatusCallbackMethod"];
        if (params["sipAuthUsername"] !== undefined)
            data["SipAuthUsername"] = params["sipAuthUsername"];
        if (params["sipAuthPassword"] !== undefined)
            data["SipAuthPassword"] = params["sipAuthPassword"];
        if (params["machineDetection"] !== undefined)
            data["MachineDetection"] = params["machineDetection"];
        if (params["machineDetectionTimeout"] !== undefined)
            data["MachineDetectionTimeout"] = params["machineDetectionTimeout"];
        if (params["recordingStatusCallbackEvent"] !== undefined)
            data["RecordingStatusCallbackEvent"] = serialize.map(params["recordingStatusCallbackEvent"], (e) => e);
        if (params["trim"] !== undefined)
            data["Trim"] = params["trim"];
        if (params["callerId"] !== undefined)
            data["CallerId"] = params["callerId"];
        if (params["machineDetectionSpeechThreshold"] !== undefined)
            data["MachineDetectionSpeechThreshold"] =
                params["machineDetectionSpeechThreshold"];
        if (params["machineDetectionSpeechEndThreshold"] !== undefined)
            data["MachineDetectionSpeechEndThreshold"] =
                params["machineDetectionSpeechEndThreshold"];
        if (params["machineDetectionSilenceTimeout"] !== undefined)
            data["MachineDetectionSilenceTimeout"] =
                params["machineDetectionSilenceTimeout"];
        if (params["asyncAmd"] !== undefined)
            data["AsyncAmd"] = params["asyncAmd"];
        if (params["asyncAmdStatusCallback"] !== undefined)
            data["AsyncAmdStatusCallback"] = params["asyncAmdStatusCallback"];
        if (params["asyncAmdStatusCallbackMethod"] !== undefined)
            data["AsyncAmdStatusCallbackMethod"] =
                params["asyncAmdStatusCallbackMethod"];
        if (params["byoc"] !== undefined)
            data["Byoc"] = params["byoc"];
        if (params["callReason"] !== undefined)
            data["CallReason"] = params["callReason"];
        if (params["callToken"] !== undefined)
            data["CallToken"] = params["callToken"];
        if (params["recordingTrack"] !== undefined)
            data["RecordingTrack"] = params["recordingTrack"];
        if (params["timeLimit"] !== undefined)
            data["TimeLimit"] = params["timeLimit"];
        if (params["url"] !== undefined)
            data["Url"] = params["url"];
        if (params["twiml"] !== undefined)
            data["Twiml"] = serialize.twiml(params["twiml"]);
        if (params["applicationSid"] !== undefined)
            data["ApplicationSid"] = params["applicationSid"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CallInstance(operationVersion, payload, instance._solution.accountSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["to"] !== undefined)
            data["To"] = params["to"];
        if (params["from"] !== undefined)
            data["From"] = params["from"];
        if (params["parentCallSid"] !== undefined)
            data["ParentCallSid"] = params["parentCallSid"];
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["startTime"] !== undefined)
            data["StartTime"] = serialize.iso8601DateTime(params["startTime"]);
        if (params["startTimeBefore"] !== undefined)
            data["StartTime<"] = serialize.iso8601DateTime(params["startTimeBefore"]);
        if (params["startTimeAfter"] !== undefined)
            data["StartTime>"] = serialize.iso8601DateTime(params["startTimeAfter"]);
        if (params["endTime"] !== undefined)
            data["EndTime"] = serialize.iso8601DateTime(params["endTime"]);
        if (params["endTimeBefore"] !== undefined)
            data["EndTime<"] = serialize.iso8601DateTime(params["endTimeBefore"]);
        if (params["endTimeAfter"] !== undefined)
            data["EndTime>"] = serialize.iso8601DateTime(params["endTimeAfter"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CallPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new CallPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.CallListInstance = CallListInstance;
class CallPage extends Page_1.default {
    /**
     * Initialize the CallPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of CallInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new CallInstance(this._version, payload, this._solution.accountSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CallPage = CallPage;
