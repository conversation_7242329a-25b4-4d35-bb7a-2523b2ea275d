"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Ip_messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserChannelPage = exports.UserChannelListInstance = exports.UserChannelInstance = exports.UserChannelContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class UserChannelContextImpl {
    constructor(_version, serviceSid, userSid, channelSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(serviceSid)) {
            throw new Error("Parameter 'serviceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(userSid)) {
            throw new Error("Parameter 'userSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(channelSid)) {
            throw new Error("Parameter 'channelSid' is not valid.");
        }
        this._solution = { serviceSid, userSid, channelSid };
        this._uri = `/Services/${serviceSid}/Users/<USER>/Channels/${channelSid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new UserChannelInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.userSid, instance._solution.channelSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["notificationLevel"] !== undefined)
            data["NotificationLevel"] = params["notificationLevel"];
        if (params["lastConsumedMessageIndex"] !== undefined)
            data["LastConsumedMessageIndex"] = params["lastConsumedMessageIndex"];
        if (params["lastConsumptionTimestamp"] !== undefined)
            data["LastConsumptionTimestamp"] = serialize.iso8601DateTime(params["lastConsumptionTimestamp"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserChannelInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.userSid, instance._solution.channelSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserChannelContextImpl = UserChannelContextImpl;
class UserChannelInstance {
    constructor(_version, payload, serviceSid, userSid, channelSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.serviceSid = payload.service_sid;
        this.channelSid = payload.channel_sid;
        this.userSid = payload.user_sid;
        this.memberSid = payload.member_sid;
        this.status = payload.status;
        this.lastConsumedMessageIndex = deserialize.integer(payload.last_consumed_message_index);
        this.unreadMessagesCount = deserialize.integer(payload.unread_messages_count);
        this.links = payload.links;
        this.url = payload.url;
        this.notificationLevel = payload.notification_level;
        this._solution = {
            serviceSid,
            userSid,
            channelSid: channelSid || this.channelSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new UserChannelContextImpl(this._version, this._solution.serviceSid, this._solution.userSid, this._solution.channelSid);
        return this._context;
    }
    /**
     * Remove a UserChannelInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a UserChannelInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed UserChannelInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            serviceSid: this.serviceSid,
            channelSid: this.channelSid,
            userSid: this.userSid,
            memberSid: this.memberSid,
            status: this.status,
            lastConsumedMessageIndex: this.lastConsumedMessageIndex,
            unreadMessagesCount: this.unreadMessagesCount,
            links: this.links,
            url: this.url,
            notificationLevel: this.notificationLevel,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserChannelInstance = UserChannelInstance;
function UserChannelListInstance(version, serviceSid, userSid) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(userSid)) {
        throw new Error("Parameter 'userSid' is not valid.");
    }
    const instance = ((channelSid) => instance.get(channelSid));
    instance.get = function get(channelSid) {
        return new UserChannelContextImpl(version, serviceSid, userSid, channelSid);
    };
    instance._version = version;
    instance._solution = { serviceSid, userSid };
    instance._uri = `/Services/${serviceSid}/Users/<USER>/Channels`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserChannelPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new UserChannelPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UserChannelListInstance = UserChannelListInstance;
class UserChannelPage extends Page_1.default {
    /**
     * Initialize the UserChannelPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of UserChannelInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new UserChannelInstance(this._version, payload, this._solution.serviceSid, this._solution.userSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserChannelPage = UserChannelPage;
