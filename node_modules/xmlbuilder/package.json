{"name": "xmlbuilder", "version": "13.0.2", "keywords": ["xml", "xmlbuilder"], "homepage": "http://github.com/oozcitak/xmlbuilder-js", "description": "An XML builder for node.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder-js.git"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder-js/issues"}, "main": "./lib/index", "typings": "./typings/index.d.ts", "engines": {"node": ">=6.0"}, "dependencies": {}, "devDependencies": {"coffee-coverage": "*", "coffeescript": "*", "coveralls": "*", "mocha": "*", "nyc": "*", "xpath": "*"}, "scripts": {"prepublishOnly": "coffee -co lib src", "test": "nyc mocha \"test/**/*.coffee\""}}