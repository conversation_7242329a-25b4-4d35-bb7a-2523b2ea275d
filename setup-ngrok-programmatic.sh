#!/bin/bash

# Script para configurar ngrok programaticamente com credenciais do .env
# Twi<PERSON>pp Typing Indicator Test

echo "🌐 Configurando ngrok Programaticamente"
echo "======================================="

# Carregar variáveis do .env
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ Arquivo .env não encontrado!"
    exit 1
fi

# Verificar se as variáveis do ngrok estão configuradas
if [ -z "$NGROK_AUTH_TOKEN" ] || [ "$NGROK_AUTH_TOKEN" = "your_ngrok_auth_token_here" ]; then
    echo "❌ NGROK_AUTH_TOKEN não configurado no arquivo .env"
    echo "📝 Configure NGROK_AUTH_TOKEN=sua_chave_aqui no arquivo .env"
    exit 1
fi

echo "✅ Credenciais ngrok carregadas do .env"
echo "📧 Email: $NGROK_EMAIL"
echo "🔑 Token: ${NGROK_AUTH_TOKEN:0:10}..."

# Verificar se ngrok está instalado
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok não encontrado!"
    echo ""
    echo "📥 Para instalar ngrok:"
    echo "1. brew install ngrok/ngrok/ngrok"
    echo "2. Ou baixe de: https://ngrok.com/"
    echo ""
    exit 1
fi

echo "✅ ngrok encontrado: $(ngrok version)"

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando na porta 3000"
    echo "🚀 Inicie o servidor primeiro com: node server.js"
    exit 1
fi

echo "✅ Servidor está rodando na porta 3000"

# Parar qualquer instância do ngrok que esteja rodando
echo "🧹 Limpando instâncias anteriores do ngrok..."
pkill -f "ngrok.*3000" 2>/dev/null || true
sleep 2

# Configurar ngrok com o novo token
echo "🔧 Configurando ngrok com novo token..."
ngrok config add-authtoken $NGROK_AUTH_TOKEN

if [ $? -ne 0 ]; then
    echo "❌ Falha ao configurar token do ngrok"
    exit 1
fi

echo "✅ Token configurado com sucesso"

# Para ngrok v3, usar comando direto sem arquivo de configuração complexo
echo "🚀 Iniciando túnel ngrok..."
ngrok http 3000 --log=stdout > ngrok.log 2>&1 &
NGROK_PID=$!

echo "⏳ Aguardando ngrok inicializar..."
sleep 8

# Verificar se ngrok iniciou corretamente
if ! kill -0 $NGROK_PID 2>/dev/null; then
    echo "❌ Falha ao iniciar ngrok"
    echo "📋 Log do ngrok:"
    cat ngrok.log
    exit 1
fi

# Obter URL pública com retry
echo "🔍 Obtendo URL pública..."
for i in {1..10}; do
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | jq -r '.tunnels[0].public_url' 2>/dev/null)
    
    if [ "$NGROK_URL" != "null" ] && [ ! -z "$NGROK_URL" ] && [[ "$NGROK_URL" == https://* ]]; then
        break
    fi
    
    echo "⏳ Tentativa $i/10 - Aguardando ngrok..."
    sleep 2
done

if [ "$NGROK_URL" = "null" ] || [ -z "$NGROK_URL" ] || [[ "$NGROK_URL" != https://* ]]; then
    echo "❌ Não foi possível obter URL do ngrok"
    echo "📋 Log do ngrok:"
    cat ngrok.log
    echo ""
    echo "🔍 Status da API do ngrok:"
    curl -s http://localhost:4040/api/tunnels 2>/dev/null | jq . || echo "API não disponível"
    exit 1
fi

echo "✅ Túnel ngrok ativo!"
echo "🌐 URL pública: $NGROK_URL"

# Gerar URLs dos webhooks
WEBHOOK_INCOMING="$NGROK_URL/webhooks/whatsapp/incoming"
WEBHOOK_STATUS="$NGROK_URL/webhooks/whatsapp/status"
WEBHOOK_CONVERSATIONS="$NGROK_URL/webhooks/conversations"

echo ""
echo "📋 URLs dos Webhooks para configurar na Twilio:"
echo "=============================================="
echo ""
echo "🔗 WhatsApp Sandbox - When a message comes in:"
echo "   $WEBHOOK_INCOMING"
echo ""
echo "🔗 WhatsApp Sandbox - Status callback URL:"
echo "   $WEBHOOK_STATUS"
echo ""
echo "🔗 Conversations Service - Webhook URL:"
echo "   $WEBHOOK_CONVERSATIONS"
echo ""

# Testar webhooks
echo "🧪 Testando endpoints dos webhooks..."
echo ""

# Teste webhook test
TEST_RESULT=$(curl -s "$NGROK_URL/webhooks/test" 2>/dev/null)
if [[ "$TEST_RESULT" =~ "Webhooks funcionando" ]]; then
    echo "✅ Webhook de teste: OK"
else
    echo "❌ Webhook de teste: FALHOU"
    echo "   Resposta: $TEST_RESULT"
fi

# Salvar URLs em arquivo para referência
cat > webhook-urls.txt << EOF
# URLs dos Webhooks - Gerado em $(date)
# Configurado com: $NGROK_EMAIL
# Configure estas URLs no Twilio Console

## WhatsApp Sandbox
When a message comes in: $WEBHOOK_INCOMING
Status callback URL: $WEBHOOK_STATUS

## Conversations Service  
Webhook URL: $WEBHOOK_CONVERSATIONS

## URLs para teste
Público: $NGROK_URL
Teste: $NGROK_URL/webhooks/test
Logs: $NGROK_URL/webhooks/logs

## Como configurar:
1. WhatsApp Sandbox:
   - Acesse: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
   - Configure "When a message comes in": $WEBHOOK_INCOMING
   - Configure "Status callback URL": $WEBHOOK_STATUS

2. Conversations Service:
   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
   - Selecione seu service
   - Configure "Webhook URL": $WEBHOOK_CONVERSATIONS
   - Marque eventos: onMessageAdded, onTypingStarted, onTypingEnded

## Comandos úteis:
- Parar túnel: pkill -f 'ngrok.*3000'
- Ver logs: tail -f ngrok.log
- Status: curl http://localhost:4040/api/tunnels | jq
EOF

echo "📄 URLs salvas em: webhook-urls.txt"
echo ""
echo "🔧 Próximos passos:"
echo "=================="
echo "1. 📋 Copie as URLs acima"
echo "2. 🌐 Acesse o Twilio Console"
echo "3. ⚙️  Configure os webhooks conforme indicado"
echo "4. 🧪 Teste enviando mensagem do WhatsApp"
echo ""
echo "💡 Comandos úteis:"
echo "   - Parar túnel: pkill -f 'ngrok.*3000'"
echo "   - Ver logs: tail -f ngrok.log"
echo "   - Status: curl http://localhost:4040/api/tunnels | jq"
echo "   - Logs da aplicação: curl $NGROK_URL/webhooks/logs"

# Salvar PID do ngrok para controle
echo $NGROK_PID > ngrok.pid

echo ""
echo "📡 Túnel ngrok configurado e ativo!"
echo "🔑 Usando token: ${NGROK_AUTH_TOKEN:0:10}..."
echo "📧 Conta: $NGROK_EMAIL"
echo "🌐 URL: $NGROK_URL"
echo ""
echo "🎯 Configure os webhooks na Twilio e teste a aplicação!"
