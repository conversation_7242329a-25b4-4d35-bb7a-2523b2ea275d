#!/bin/bash

# Script de teste para a nova funcionalidade inteligente de conversa
# Twilio WhatsApp Typing Indicator Test

echo "🧠 Testando Sistema Inteligente de Conversa"
echo "==========================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: node server.js"
    exit 1
fi

echo "✅ Servidor está rodando"

# Função para executar teste
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    echo -n "🔍 $test_name... "
    
    result=$(eval "$test_command" 2>&1)
    exit_code=$?
    
    if [ $exit_code -eq 0 ] && [[ "$result" =~ $expected_pattern ]]; then
        echo "✅ PASSOU"
        return 0
    else
        echo "❌ FALHOU"
        echo "   Resultado: $result"
        return 1
    fi
}

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

echo ""
echo "🧪 Fase 1: Testes Básicos"
echo "========================="

# Teste 1: Verificação de Credenciais
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Verificação de Credenciais" "curl -s http://localhost:3000/api/auth/verify" '"status":"OK"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 2: Geração de Token
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Geração de Token" "curl -s -X POST http://localhost:3000/api/auth/token -H 'Content-Type: application/json' -d '{\"identity\":\"test-intelligent\"}'" '"token"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

echo ""
echo "🧠 Fase 2: Sistema Inteligente"
echo "=============================="

# Limpar participante WhatsApp primeiro
echo "🧹 Limpando participante WhatsApp existente..."
curl -s -X DELETE http://localhost:3000/api/conversations/cleanup-whatsapp/+5551993590210 > /dev/null

# Teste 3: Primeira Conexão (deve criar nova conversa)
TOTAL_TESTS=$((TOTAL_TESTS + 1))
echo -n "🔍 Primeira Conexão (criar nova)... "
result=$(curl -s -X POST http://localhost:3000/api/conversations/connect-whatsapp -H 'Content-Type: application/json' -d '{"whatsappNumber":"+5551993590210","friendlyName":"Teste Inteligente 1","userIdentity":"test-user-1"}')

if [[ "$result" =~ "\"success\":true" ]] && [[ "$result" =~ "\"isNewConversation\":true" ]]; then
    echo "✅ PASSOU (nova conversa criada)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
    
    # Extrair SID da conversa
    CONVERSATION_SID=$(echo "$result" | grep -o '"conversationSid":"[^"]*"' | cut -d'"' -f4)
    echo "   📝 SID da conversa: $CONVERSATION_SID"
else
    echo "❌ FALHOU"
    echo "   Resultado: $result"
fi

# Teste 4: Segunda Conexão (deve usar conversa existente)
TOTAL_TESTS=$((TOTAL_TESTS + 1))
echo -n "🔍 Segunda Conexão (usar existente)... "
result=$(curl -s -X POST http://localhost:3000/api/conversations/connect-whatsapp -H 'Content-Type: application/json' -d '{"whatsappNumber":"+5551993590210","friendlyName":"Teste Inteligente 2","userIdentity":"test-user-1"}')

if [[ "$result" =~ "\"success\":true" ]] && [[ "$result" =~ "\"isNewConversation\":false" ]] && [[ "$result" =~ "Conectado à conversa existente" ]]; then
    echo "✅ PASSOU (conversa existente reutilizada)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FALHOU"
    echo "   Resultado: $result"
fi

# Teste 5: Terceira Conexão com usuário diferente (deve adicionar à conversa existente)
TOTAL_TESTS=$((TOTAL_TESTS + 1))
echo -n "🔍 Terceira Conexão (novo usuário)... "
result=$(curl -s -X POST http://localhost:3000/api/conversations/connect-whatsapp -H 'Content-Type: application/json' -d '{"whatsappNumber":"+5551993590210","friendlyName":"Teste Inteligente 3","userIdentity":"test-user-2"}')

if [[ "$result" =~ "\"success\":true" ]] && [[ "$result" =~ "\"isNewConversation\":false" ]]; then
    echo "✅ PASSOU (novo usuário adicionado à conversa existente)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FALHOU"
    echo "   Resultado: $result"
fi

echo ""
echo "🔍 Fase 3: Validações"
echo "===================="

# Teste 6: Validação de Formato de Número
TOTAL_TESTS=$((TOTAL_TESTS + 1))
echo -n "🔍 Validação de Formato Inválido... "
result=$(curl -s -X POST http://localhost:3000/api/conversations/connect-whatsapp -H 'Content-Type: application/json' -d '{"whatsappNumber":"123456","friendlyName":"Teste Erro","userIdentity":"test-user"}')

if [[ "$result" =~ "Formato do número WhatsApp inválido" ]]; then
    echo "✅ PASSOU (rejeitou número inválido corretamente)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FALHOU"
    echo "   Resultado: $result"
fi

# Teste 7: Validação de Campos Obrigatórios
TOTAL_TESTS=$((TOTAL_TESTS + 1))
echo -n "🔍 Validação de Campos Obrigatórios... "
result=$(curl -s -X POST http://localhost:3000/api/conversations/connect-whatsapp -H 'Content-Type: application/json' -d '{"friendlyName":"Teste Erro"}')

if [[ "$result" =~ "Número do WhatsApp é obrigatório" ]]; then
    echo "✅ PASSOU (rejeitou campos faltando corretamente)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FALHOU"
    echo "   Resultado: $result"
fi

echo ""
echo "🌐 Fase 4: Interface Web"
echo "======================="

# Teste 8: Interface Web
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Interface Web" "curl -s http://localhost:3000/" "Conectar ao WhatsApp"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 9: Verificar se o número está pré-configurado
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s http://localhost:3000/)
if [[ "$result" =~ "value=\"+5551993590210\"" ]]; then
    echo "🔍 Número Pré-configurado... ✅ PASSOU"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Número Pré-configurado... ❌ FALHOU"
fi

# Teste 10: Verificar mensagens de ajuda
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s http://localhost:3000/)
if [[ "$result" =~ "usar conversa existente ou criar uma nova automaticamente" ]]; then
    echo "🔍 Mensagens de Ajuda... ✅ PASSOU"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Mensagens de Ajuda... ❌ FALHOU"
fi

# Resultado final
echo ""
echo "📊 Resultado dos Testes do Sistema Inteligente"
echo "=============================================="
echo "✅ Testes Passaram: $PASSED_TESTS"
echo "❌ Testes Falharam: $((TOTAL_TESTS - PASSED_TESTS))"
echo "📈 Total de Testes: $TOTAL_TESTS"
echo "📊 Taxa de Sucesso: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    echo "🎉 TODOS OS TESTES DO SISTEMA INTELIGENTE PASSARAM!"
    echo "🧠 O sistema agora:"
    echo "   ✅ Detecta conversas existentes automaticamente"
    echo "   ✅ Reutiliza conversas quando possível"
    echo "   ✅ Cria novas conversas apenas quando necessário"
    echo "   ✅ Adiciona participantes sem duplicação"
    echo "   ✅ Trata erros de forma inteligente"
    echo ""
    echo "🚀 Próximos passos:"
    echo "   1. Acesse: http://localhost:3000"
    echo "   2. Clique em 'Conectar'"
    echo "   3. Clique em '🚀 Conectar ao WhatsApp'"
    echo "   4. Observe que não há mais erros de 'Participant already exists'"
    echo "   5. Teste o typing indicator!"
    exit 0
else
    echo ""
    echo "⚠️  Alguns testes falharam. Verifique a implementação."
    exit 1
fi
