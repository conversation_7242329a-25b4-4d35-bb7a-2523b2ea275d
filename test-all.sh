#!/bin/bash

# Script de teste completo para Mac
# Twilio WhatsApp Typing Indicator Test

echo "🧪 Executando Testes Completos"
echo "==============================="

# Função para verificar se o servidor está rodando
check_server() {
    curl -s http://localhost:3000/health > /dev/null
    return $?
}

# Função para executar teste
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    echo -n "🔍 $test_name... "
    
    result=$(eval "$test_command" 2>&1)
    exit_code=$?
    
    if [ $exit_code -eq 0 ] && [[ "$result" =~ $expected_pattern ]]; then
        echo "✅ PASSOU"
        return 0
    else
        echo "❌ FALHOU"
        echo "   Resultado: $result"
        return 1
    fi
}

# Verificar se o servidor está rodando
if ! check_server; then
    echo "⚠️  Servidor não está rodando. Iniciando..."
    node server.js &
    SERVER_PID=$!
    sleep 3
    
    if ! check_server; then
        echo "❌ Falha ao iniciar servidor"
        exit 1
    fi
    echo "✅ Servidor iniciado"
else
    echo "✅ Servidor já está rodando"
    SERVER_PID=""
fi

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

# Teste 1: Health Check
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Health Check" "curl -s http://localhost:3000/health" '"status":"OK"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 2: Verificação de Credenciais
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Verificação de Credenciais" "curl -s http://localhost:3000/api/auth/verify" '"status":"OK"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 3: Geração de Token
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Geração de Token" "curl -s -X POST http://localhost:3000/api/auth/token -H 'Content-Type: application/json' -d '{\"identity\":\"test-user\"}'" '"token"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 4: Criação de Conversa
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Criação de Conversa" "curl -s -X POST http://localhost:3000/api/conversations/create -H 'Content-Type: application/json' -d '{\"friendlyName\":\"Teste Automatizado\"}'" '"conversationSid"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
    
    # Extrair SID da conversa para próximos testes
    CONVERSATION_SID=$(curl -s -X POST http://localhost:3000/api/conversations/create -H 'Content-Type: application/json' -d '{"friendlyName":"Teste Participante"}' | grep -o '"conversationSid":"[^"]*"' | cut -d'"' -f4)
fi

# Teste 5: Listagem de Conversas
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Listagem de Conversas" "curl -s http://localhost:3000/api/conversations" '"conversations"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 6: Adição de Participante Chat (se temos SID)
if [ ! -z "$CONVERSATION_SID" ]; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "Adição de Participante Chat" "curl -s -X POST http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat -H 'Content-Type: application/json' -d '{\"identity\":\"test-participant\"}'" '"participantSid"'; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
fi

# Teste 7: Interface Web
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Interface Web" "curl -s -I http://localhost:3000/" "200 OK"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 8: Arquivos Estáticos CSS
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Arquivos CSS" "curl -s -I http://localhost:3000/style.css" "text/css"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 9: Arquivos Estáticos JS
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Arquivos JavaScript" "curl -s -I http://localhost:3000/script.js" "application/javascript"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Parar servidor se foi iniciado por este script
if [ ! -z "$SERVER_PID" ]; then
    echo "🛑 Parando servidor..."
    kill $SERVER_PID 2>/dev/null || true
fi

# Resultado final
echo ""
echo "📊 Resultado dos Testes"
echo "======================="
echo "✅ Testes Passaram: $PASSED_TESTS"
echo "❌ Testes Falharam: $((TOTAL_TESTS - PASSED_TESTS))"
echo "📈 Total de Testes: $TOTAL_TESTS"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    echo "🎉 TODOS OS TESTES PASSARAM!"
    echo "🚀 O projeto está 100% funcional no seu Mac!"
    exit 0
else
    echo ""
    echo "⚠️  Alguns testes falharam. Verifique a configuração."
    exit 1
fi
