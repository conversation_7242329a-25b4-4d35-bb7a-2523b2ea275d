#!/bin/bash

# Script alternativo usando localtunnel (não requer conta)
# Twilio WhatsApp Typing Indicator Test

echo "🌐 Configurando Túnel Público com LocalTunnel"
echo "============================================="
echo ""
echo "💡 LocalTunnel é uma alternativa gratuita ao ngrok que não requer conta"
echo ""

# Verificar se Node.js está instalado
if ! command -v npm &> /dev/null; then
    echo "❌ npm não encontrado!"
    echo "📥 Instale Node.js primeiro: https://nodejs.org/"
    exit 1
fi

echo "✅ npm encontrado: $(npm --version)"

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando na porta 3000"
    echo "🚀 Inicie o servidor primeiro com: node server.js"
    exit 1
fi

echo "✅ Servidor está rodando na porta 3000"

# Instalar localtunnel se não estiver instalado
if ! command -v lt &> /dev/null; then
    echo "📦 Instalando localtunnel..."
    npm install -g localtunnel
    
    if [ $? -ne 0 ]; then
        echo "❌ Falha ao instalar localtunnel"
        echo "💡 Tente: sudo npm install -g localtunnel"
        exit 1
    fi
fi

echo "✅ localtunnel encontrado: $(lt --version)"

# Parar qualquer instância anterior
echo "🧹 Limpando túneis anteriores..."
pkill -f "lt --port" 2>/dev/null || true
sleep 2

# Gerar um subdomínio único baseado no timestamp
SUBDOMAIN="twilio-$(date +%s)"

echo "🚀 Iniciando túnel localtunnel..."
echo "📝 Subdomínio: $SUBDOMAIN"

# Iniciar localtunnel
lt --port 3000 --subdomain $SUBDOMAIN > localtunnel.log 2>&1 &
LT_PID=$!

echo "⏳ Aguardando túnel inicializar..."
sleep 5

# Verificar se localtunnel iniciou
if ! kill -0 $LT_PID 2>/dev/null; then
    echo "❌ Falha ao iniciar localtunnel"
    echo "📋 Log:"
    cat localtunnel.log
    exit 1
fi

# Obter URL do túnel
TUNNEL_URL="https://$SUBDOMAIN.loca.lt"

echo "✅ Túnel localtunnel ativo!"
echo "🌐 URL pública: $TUNNEL_URL"

# Gerar URLs dos webhooks
WEBHOOK_INCOMING="$TUNNEL_URL/webhooks/whatsapp/incoming"
WEBHOOK_STATUS="$TUNNEL_URL/webhooks/whatsapp/status"
WEBHOOK_CONVERSATIONS="$TUNNEL_URL/webhooks/conversations"

echo ""
echo "📋 URLs dos Webhooks para configurar na Twilio:"
echo "=============================================="
echo ""
echo "🔗 WhatsApp Sandbox - When a message comes in:"
echo "   $WEBHOOK_INCOMING"
echo ""
echo "🔗 WhatsApp Sandbox - Status callback URL:"
echo "   $WEBHOOK_STATUS"
echo ""
echo "🔗 Conversations Service - Webhook URL:"
echo "   $WEBHOOK_CONVERSATIONS"
echo ""

# Testar webhooks
echo "🧪 Testando endpoints dos webhooks..."
echo ""

# Teste webhook test
TEST_RESULT=$(curl -s "$TUNNEL_URL/webhooks/test" 2>/dev/null)
if [[ "$TEST_RESULT" =~ "Webhooks funcionando" ]]; then
    echo "✅ Webhook de teste: OK"
else
    echo "❌ Webhook de teste: FALHOU"
    echo "   Resposta: $TEST_RESULT"
    echo ""
    echo "💡 Primeira vez usando este subdomínio?"
    echo "   Acesse $TUNNEL_URL no navegador e clique em 'Click to Continue'"
fi

# Salvar URLs em arquivo
cat > webhook-urls-localtunnel.txt << EOF
# URLs dos Webhooks LocalTunnel - Gerado em $(date)
# Configure estas URLs no Twilio Console

## WhatsApp Sandbox
When a message comes in: $WEBHOOK_INCOMING
Status callback URL: $WEBHOOK_STATUS

## Conversations Service  
Webhook URL: $WEBHOOK_CONVERSATIONS

## URLs para teste
Público: $TUNNEL_URL
Teste: $TUNNEL_URL/webhooks/test
Logs: $TUNNEL_URL/webhooks/logs

## Como configurar:
1. WhatsApp Sandbox:
   - Acesse: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
   - Configure "When a message comes in": $WEBHOOK_INCOMING
   - Configure "Status callback URL": $WEBHOOK_STATUS

2. Conversations Service:
   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
   - Selecione seu service
   - Configure "Webhook URL": $WEBHOOK_CONVERSATIONS
   - Marque eventos: onMessageAdded, onTypingStarted, onTypingEnded

## Comandos úteis:
- Parar túnel: pkill -f 'lt --port'
- Ver logs: tail -f localtunnel.log
- Novo túnel: ./setup-localtunnel.sh

## IMPORTANTE:
- LocalTunnel pode pedir para "Click to Continue" na primeira vez
- Acesse $TUNNEL_URL no navegador se os webhooks não funcionarem
- O subdomínio muda a cada execução do script
EOF

echo "📄 URLs salvas em: webhook-urls-localtunnel.txt"

# Salvar PID para controle
echo $LT_PID > localtunnel.pid

echo ""
echo "🔧 Próximos passos:"
echo "=================="
echo "1. 📋 Copie as URLs acima"
echo "2. 🌐 Acesse o Twilio Console"
echo "3. ⚙️  Configure os webhooks conforme indicado"
echo "4. 🧪 Se webhooks falharem, acesse $TUNNEL_URL no navegador primeiro"
echo ""
echo "💡 Comandos úteis:"
echo "   - Parar túnel: pkill -f 'lt --port'"
echo "   - Ver logs: tail -f localtunnel.log"
echo "   - Logs da aplicação: curl $TUNNEL_URL/webhooks/logs"

echo ""
echo "📡 Túnel LocalTunnel configurado e ativo!"
echo "🌐 URL: $TUNNEL_URL"
echo "📝 Subdomínio: $SUBDOMAIN"
echo ""
echo "⚠️  IMPORTANTE: Se for a primeira vez usando este subdomínio,"
echo "   acesse $TUNNEL_URL no navegador e clique em 'Click to Continue'"
echo ""
echo "🎯 Configure os webhooks na Twilio e teste a aplicação!"
