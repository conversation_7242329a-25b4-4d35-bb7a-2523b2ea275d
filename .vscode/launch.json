{"version": "0.2.0", "configurations": [{"name": "Launch Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server.js", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server.js", "env": {"NODE_ENV": "development", "DEBUG": "*"}, "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"]}]}