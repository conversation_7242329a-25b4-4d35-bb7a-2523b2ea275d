# 🚀 Twilio WhatsApp Typing Indicator Test

Este projeto foi criado para testar a funcionalidade de **Typing Indicator** da Twilio Conversations API com WhatsApp. Permite criar conversas, adicionar participantes do WhatsApp e testar o indicador de digitação em tempo real.

## 📋 Funcionalidades

- ✅ Conexão com Twilio Conversations SDK
- ✅ Criação de conversas
- ✅ Adição de participantes WhatsApp
- ✅ Envio e recebimento de mensagens
- ✅ **Typing Indicator** (foco principal)
- ✅ Interface web intuitiva
- ✅ Logs em tempo real
- ✅ Suporte ao Twilio Sandbox para WhatsApp

## 🛠️ Tecnologias Utilizadas

- **Backend**: Node.js, Express.js
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **API**: Twilio Conversations SDK
- **Styling**: CSS Grid, Flexbox, Animações CSS

## 📦 Instalação

### 1. Clone o repositório
```bash
git clone <url-do-repositorio>
cd twiliowhats
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas credenciais da Twilio (veja seção de configuração abaixo).

### 4. Execute o projeto

#### 🍎 Para usuários Mac (Recomendado):
```bash
# Inicialização automática com verificações
./start-mac.sh

# Desenvolvimento com auto-reload
./dev-mac.sh

# Executar todos os testes
./test-all.sh
```

#### 💻 Comandos tradicionais:
```bash
# Desenvolvimento (com nodemon)
npm run dev

# Produção
npm start

# Testes
npm test
```

### 5. Acesse a aplicação
Abra seu navegador em: `http://localhost:3000`

## ⚙️ Configuração da Twilio

Antes de usar a aplicação, você precisa configurar alguns recursos na Twilio. Consulte o arquivo `docs/TWILIO_SETUP.md` para instruções detalhadas.

### Variáveis de Ambiente Necessárias

```env
# Credenciais básicas da Twilio
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here

# Credenciais da API (para gerar tokens)
TWILIO_API_KEY=your_api_key_here
TWILIO_API_SECRET=your_api_secret_here

# Service SID do Conversations (opcional, mas recomendado)
TWILIO_CONVERSATIONS_SERVICE_SID=your_service_sid_here

# Configurações do WhatsApp Sandbox
TWILIO_WHATSAPP_SANDBOX_NUMBER=whatsapp:+***********
TWILIO_SANDBOX_KEYWORD=your_sandbox_keyword_here

# Configurações do servidor
PORT=3000
NODE_ENV=development
```

## 🚀 Como Usar

### 1. Preparar o WhatsApp
- Acesse o [Twilio Console](https://console.twilio.com)
- Vá para **Messaging > Try it out > Send a WhatsApp message**
- Envie `join [seu-keyword]` para o número sandbox da Twilio

### 2. Usar a Aplicação
1. **Conectar**: Insira uma identidade e clique em "Conectar"
2. **Criar Conversa**: Digite um nome e clique em "Criar Conversa"
3. **Adicionar WhatsApp**: Digite o número do WhatsApp e adicione à conversa
4. **Testar Typing**: Digite no campo de mensagem para enviar o typing indicator
5. **Verificar**: Observe no WhatsApp se o typing indicator aparece

### 3. Testar o Typing Indicator
- Digite no campo de mensagem da aplicação web
- O typing indicator será enviado automaticamente
- Verifique no WhatsApp se aparece "digitando..."
- Teste também digitando no WhatsApp e vendo na aplicação web

## 📁 Estrutura do Projeto

```
twiliowhats/
├── public/                 # Frontend
│   ├── index.html         # Interface principal
│   ├── style.css          # Estilos
│   └── script.js          # Lógica do frontend
├── routes/                # Rotas da API
│   ├── auth.js           # Autenticação e tokens
│   └── conversations.js  # Gerenciamento de conversas
├── docs/                 # Documentação
│   └── TWILIO_SETUP.md   # Guia de configuração da Twilio
├── server.js             # Servidor principal
├── package.json          # Dependências
├── .env.example          # Template de variáveis
└── README.md             # Este arquivo
```

## 🔧 API Endpoints

### Autenticação
- `POST /api/auth/token` - Gerar token de acesso
- `GET /api/auth/verify` - Verificar configuração

### Conversas
- `POST /api/conversations/create` - Criar conversa
- `GET /api/conversations` - Listar conversas
- `POST /api/conversations/:sid/participants/whatsapp` - Adicionar WhatsApp
- `POST /api/conversations/:sid/participants/chat` - Adicionar participante chat
- `POST /api/conversations/:sid/messages` - Enviar mensagem

## 🐛 Troubleshooting

### Problemas Comuns

1. **Erro de conexão**
   - Verifique se as credenciais estão corretas
   - Confirme se o Account SID e Auth Token estão válidos

2. **Token inválido**
   - Verifique se API Key e API Secret estão configurados
   - Confirme se o Service SID está correto

3. **WhatsApp não recebe mensagens**
   - Confirme se o número está conectado ao sandbox
   - Verifique se enviou a mensagem "join [keyword]"
   - Confirme se o número está no formato correto (+*************)

4. **Typing indicator não funciona**
   - Confirme se ambos os participantes estão conectados
   - Verifique se a conversa está ativa
   - Teste com diferentes navegadores

### Logs e Debug

A aplicação possui logs detalhados na interface web. Use a seção "Logs de Eventos" para acompanhar:
- Conexões
- Criação de conversas
- Adição de participantes
- Envio de mensagens
- Eventos de typing

## 📚 Recursos Adicionais

- [Documentação Twilio Conversations](https://www.twilio.com/docs/conversations)
- [Typing Indicator API](https://www.twilio.com/docs/conversations/typing-indicator)
- [WhatsApp com Conversations](https://www.twilio.com/docs/conversations/using-whatsapp-conversations)
- [Twilio Sandbox para WhatsApp](https://www.twilio.com/docs/whatsapp/sandbox)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Se você encontrar problemas ou tiver dúvidas:

1. Consulte a documentação da Twilio
2. Verifique os logs da aplicação
3. Abra uma issue no repositório
4. Entre em contato com o suporte da Twilio

---

**Nota**: Este projeto é para fins de teste e desenvolvimento. Para uso em produção, implemente autenticação adequada, validação de entrada e outras medidas de segurança necessárias.
