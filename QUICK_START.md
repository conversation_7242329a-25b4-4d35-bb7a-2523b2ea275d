# 🚀 Quick Start - <PERSON><PERSON><PERSON> WhatsApp Typing Indicator

## ⚡ <PERSON><PERSON><PERSON>

### 1. Configurar Credenciais
Edite o arquivo `.env` e preencha suas credenciais da <PERSON>wi<PERSON>:

```env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_API_KEY=SKxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_API_SECRET=your_api_secret_here
TWILIO_CONVERSATIONS_SERVICE_SID=ISxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 2. Iniciar o Servidor
```bash
npm start
# ou para desenvolvimento:
npm run dev
```

### 3. Acessar a Aplicação
Abra: http://localhost:3000

### 4. Configurar WhatsApp
1. <PERSON><PERSON> o [<PERSON><PERSON><PERSON>](https://console.twilio.com)
2. Vá para **Messaging > Try it out > Send a WhatsApp message**
3. Envie `join [seu-keyword]` para o número sandbox

### 5. Testar
1. **Conectar** na aplicação web
2. **Criar conversa**
3. **Adicionar seu número WhatsApp**
4. **Digitar** no campo de mensagem
5. **Verificar** no WhatsApp se aparece "digitando..."

## 📋 Checklist de Configuração

- [ ] Credenciais da Twilio configuradas
- [ ] Conversations Service criado
- [ ] WhatsApp conectado ao sandbox
- [ ] Aplicação rodando em localhost:3000
- [ ] Teste de typing indicator funcionando

## 🆘 Problemas Comuns

**Erro de credenciais**: Verifique se todas as variáveis estão preenchidas no `.env`

**WhatsApp não recebe**: Confirme se enviou `join [keyword]` para o sandbox

**Typing não funciona**: Verifique se ambos os participantes estão na mesma conversa

## 📚 Documentação Completa

- `README.md` - Documentação completa
- `docs/TWILIO_SETUP.md` - Guia detalhado de configuração da Twilio

---

**Dica**: Use os logs na aplicação web para acompanhar o que está acontecendo!
