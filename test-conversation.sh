#!/bin/bash

# Script de teste para a nova funcionalidade de conversa unificada
# Twilio WhatsApp Typing Indicator Test

echo "🧪 Testando Nova Funcionalidade de Conversa Unificada"
echo "====================================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: node server.js"
    exit 1
fi

echo "✅ Servidor está rodando"

# Função para executar teste
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    echo -n "🔍 $test_name... "
    
    result=$(eval "$test_command" 2>&1)
    exit_code=$?
    
    if [ $exit_code -eq 0 ] && [[ "$result" =~ $expected_pattern ]]; then
        echo "✅ PASSOU"
        return 0
    else
        echo "❌ FALHOU"
        echo "   Resultado: $result"
        return 1
    fi
}

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

# Teste 1: Verificação de Credenciais
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Verificação de Credenciais" "curl -s http://localhost:3000/api/auth/verify" '"status":"OK"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 2: Geração de Token
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Geração de Token" "curl -s -X POST http://localhost:3000/api/auth/token -H 'Content-Type: application/json' -d '{\"identity\":\"test-unified\"}'" '"token"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 3: Criação de Conversa
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Criação de Conversa" "curl -s -X POST http://localhost:3000/api/conversations/create -H 'Content-Type: application/json' -d '{\"friendlyName\":\"Teste Unificado\"}'" '"conversationSid"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
    
    # Extrair SID da conversa para próximos testes
    CONVERSATION_SID=$(curl -s -X POST http://localhost:3000/api/conversations/create -H 'Content-Type: application/json' -d '{"friendlyName":"Teste WhatsApp Unificado"}' | grep -o '"conversationSid":"[^"]*"' | cut -d'"' -f4)
    echo "   📝 SID da conversa: $CONVERSATION_SID"
fi

# Teste 4: Adição de Participante Chat (se temos SID)
if [ ! -z "$CONVERSATION_SID" ]; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "Adição de Participante Chat" "curl -s -X POST http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat -H 'Content-Type: application/json' -d '{\"identity\":\"test-unified-user\"}'" '"participantSid"'; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
fi

# Teste 5: Adição de Participante WhatsApp (se temos SID)
if [ ! -z "$CONVERSATION_SID" ]; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "Adição de Participante WhatsApp" "curl -s -X POST http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/whatsapp -H 'Content-Type: application/json' -d '{\"whatsappNumber\":\"+5551993590210\"}'" '"participantSid"'; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
fi

# Teste 6: Validação de Formato de Número (deve falhar)
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s -X POST http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/whatsapp -H 'Content-Type: application/json' -d '{"whatsappNumber":"123456"}')
if [[ "$result" =~ "Formato do número WhatsApp inválido" ]]; then
    echo "🔍 Validação de Formato Inválido... ✅ PASSOU (rejeitou número inválido corretamente)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Validação de Formato Inválido... ❌ FALHOU"
    echo "   Resultado: $result"
fi

# Teste 7: Interface Web
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Interface Web" "curl -s http://localhost:3000/" "Iniciar Conversa WhatsApp"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 8: Verificar se o número está pré-configurado
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s http://localhost:3000/)
if [[ "$result" =~ "value=\"+5551993590210\"" ]]; then
    echo "🔍 Número Pré-configurado... ✅ PASSOU"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Número Pré-configurado... ❌ FALHOU"
    echo "   Não encontrou value=\"+5551993590210\" no HTML"
fi

# Resultado final
echo ""
echo "📊 Resultado dos Testes da Nova Funcionalidade"
echo "=============================================="
echo "✅ Testes Passaram: $PASSED_TESTS"
echo "❌ Testes Falharam: $((TOTAL_TESTS - PASSED_TESTS))"
echo "📈 Total de Testes: $TOTAL_TESTS"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    echo "🎉 TODOS OS TESTES DA NOVA FUNCIONALIDADE PASSARAM!"
    echo "🚀 A conversa unificada está funcionando perfeitamente!"
    echo ""
    echo "📱 Próximos passos:"
    echo "   1. Acesse: http://localhost:3000"
    echo "   2. Clique em 'Conectar'"
    echo "   3. Clique em '🚀 Iniciar Conversa com WhatsApp'"
    echo "   4. Teste o typing indicator!"
    exit 0
else
    echo ""
    echo "⚠️  Alguns testes falharam. Verifique a configuração."
    exit 1
fi
